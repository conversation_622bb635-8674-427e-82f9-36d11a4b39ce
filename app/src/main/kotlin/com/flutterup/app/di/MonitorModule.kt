package com.flutterup.app.di

import com.flutterup.app.utils.AppStatusMonitor
import com.flutterup.app.utils.AppStatusMonitorImpl
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.GlobalSettingsMonitorImpl
import com.flutterup.app.utils.UserOnlineMonitor
import com.flutterup.app.utils.UserOnlineMonitorImpl
import com.flutterup.app.utils.UserUnreadMonitorImpl
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.app.utils.chat.ChatMonitor
import com.flutterup.app.utils.chat.ChatMonitorImpl
import com.flutterup.app.utils.chat.core.ChatMessageExpansionReceiverMonitor
import com.flutterup.app.utils.chat.core.ChatMessageExpansionReceiverMonitorImpl
import com.flutterup.app.utils.chat.core.ChatMessageMonitor
import com.flutterup.app.utils.chat.core.ChatMessageMonitorImpl
import com.flutterup.app.utils.chat.core.ChatRecallMonitor
import com.flutterup.app.utils.chat.core.ChatRecallMonitorImpl
import com.flutterup.app.utils.chat.core.ChatUnreadMonitor
import com.flutterup.app.utils.chat.core.ChatUnreadMonitorImpl
import com.flutterup.app.utils.chat.core.internal.ChatDataSyncMonitor
import com.flutterup.app.utils.chat.core.internal.ChatDataSyncMonitorImpl
import com.flutterup.app.utils.chat.core.internal.ChatSystemMessageMonitor
import com.flutterup.app.utils.chat.core.internal.ChatSystemMessageMonitorImpl
import com.flutterup.app.utils.chat.core.internal.ChatUserMessageMonitor
import com.flutterup.app.utils.chat.core.internal.ChatUserMessageMonitorImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent


@Module
@InstallIn(SingletonComponent::class)
abstract class MonitorModule {

    @Binds
    abstract fun bindUserUnreadMonitor(monitor: UserUnreadMonitorImpl): UserUnreadMonitor

    @Binds
    abstract fun bindAppStatusMonitor(monitor: AppStatusMonitorImpl): AppStatusMonitor

    @Binds
    abstract fun bindGlobalSettingsMonitor(monitor: GlobalSettingsMonitorImpl): GlobalSettingsMonitor

    @Binds
    abstract fun bindChatMonitor(monitor: ChatMonitorImpl): ChatMonitor

    @Binds
    abstract fun bindOnlineMonitor(monitor: UserOnlineMonitorImpl): UserOnlineMonitor

    @Binds
    abstract fun bindChatMessageMonitor(monitor: ChatMessageMonitorImpl): ChatMessageMonitor

    @Binds
    abstract fun bindChatUnreadMonitor(monitor: ChatUnreadMonitorImpl): ChatUnreadMonitor

    @Binds
    abstract fun bindChatExpansionReceiverMonitor(monitor: ChatMessageExpansionReceiverMonitorImpl): ChatMessageExpansionReceiverMonitor

    @Binds
    abstract fun bindChatRecallMonitor(monitor: ChatRecallMonitorImpl): ChatRecallMonitor

    @Binds
    abstract fun bindChatSyncMonitor(monitor: ChatDataSyncMonitorImpl): ChatDataSyncMonitor

    @Binds
    abstract fun bindChatSystemMessageMonitor(monitor: ChatSystemMessageMonitorImpl): ChatSystemMessageMonitor

    @Binds
    abstract fun bindChatUserMessageMonitor(monitor: ChatUserMessageMonitorImpl): ChatUserMessageMonitor
}