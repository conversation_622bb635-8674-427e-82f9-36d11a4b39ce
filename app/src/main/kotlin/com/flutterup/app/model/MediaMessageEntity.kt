package com.flutterup.app.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.flutterup.app.model.MediaItemEntity.Companion.TYPE_IMAGE
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
@JsonClass(generateAdapter = true)
data class MultiMediaMessageEntity(
    @Json(name = "original_msgid")
    val msgId: String = "",

    @<PERSON>son(name = "type")
    val type: Int = TYPE_IMAGE,

    @<PERSON>son(name = "media_ids")
    val ids: String = "",

    @<PERSON><PERSON>(name = "num")
    val num: Int = 0,

    @<PERSON>son(name = "list")
    var entities: List<MediaMessageEntity> = emptyList()
) : Parcelable


@Keep
@Parcelize
@JsonClass(generateAdapter = true)
data class MediaMessageEntity(
    @Json(name = "id")
    val id: Long = 0,

    @<PERSON>son(name = "original_msgid")
    val msgId: String = "",

    @Json(name = "url")
    val url: String = "",

    @Json(name = "thumb_url")
    val thumbUrl: String = "",

    @<PERSON>son(name = "type")
    val type: Int = TYPE_IMAGE,

    @Json(name = "duration")
    val duration: Int = 0,

    @Json(name = "localStatus")
    val localStatus: Int = 0,

    @Json(name = "addSrvId")
    val addSrvId: Long = 0,

    @Json(name = "actionType")
    val actionType: Int = 0,

    @Json(name = "favorite")
    val favorite: Boolean = false,

    @Json(name = "status")
    val status: Int = STATUS_UPLOAD_NONE,

    @Json(name = "next_send_time")
    val nextSendTime: Long = 0,

    @Json(name = "send_type")
    val sendType: Int = SEND_TYPE_PUBLIC,

    @Json(name = "is_private")
    val isPrivate: Int = 0,

    @Json(name = "imId")
    val imId: String? = "",
) : Parcelable {

    val imageUrl: String get() = if (type == TYPE_IMAGE) url else thumbUrl.ifEmpty { url }

    val videoUrl: String get() = url

    companion object {
        const val STATUS_UPLOAD_NONE = 0
        const val STATUS_UPLOADING = 1
        const val STATUS_UPLOAD_OK = 2

        const val SEND_TYPE_PUBLIC = 0
        const val SEND_TYPE_PRIVATE = 1
    }
}