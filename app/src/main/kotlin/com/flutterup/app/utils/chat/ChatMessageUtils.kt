package com.flutterup.app.utils.chat

import com.flutterup.app.R
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.MediaMessageEntity
import com.flutterup.base.BaseApplication
import com.flutterup.chat.message.content.MultiPrivateMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import com.flutterup.chat.message.content.PublicMessageContent
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.message.ImageMessage
import io.rong.message.SightMessage
import io.rong.message.TextMessage

object ChatMessageUtils {

    fun convert(content: MessageContent?): String {
        if (content == null) return ""

        return when(content) {
            is TextMessage -> {
                content.content ?: ""
            }

            is SightMessage -> "[Video]"
            is ImageMessage -> "[Photo]"

            is PublicMessageContent -> {
                val entity = content.get(MediaMessageEntity::class.java)
                if (entity?.type == MediaItemEntity.TYPE_IMAGE) {
                    "[Photo]"
                } else {
                    "[Video]"
                }
            }

            is PrivateMessageContent, is MultiPrivateMessageContent -> {
                val entity = content.get(MediaMessageEntity::class.java)
                if (entity?.type == MediaItemEntity.TYPE_IMAGE) {
                    "[Private Photo]"
                } else {
                    "[Private Video]"
                }
            }

            else -> {
                BaseApplication.getApplicationContext().getString(R.string.unknown_message)
            }
        }
    }

    fun convert(message: Message): String {
        val content = message.content ?: return ""
        return convert(content)
    }
}