package com.flutterup.app.utils.chat

import com.flutterup.base.utils.resumeIfActive
import com.flutterup.chat.message.content.GiftMessageContent
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCommonDefine
import io.rong.imlib.RongCoreClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.Message.MessageDirection
import kotlinx.coroutines.suspendCancellableCoroutine

object ChatConversationUtils {

    suspend fun isExistUnreadReceivedGift(conversation: Conversation): Bo<PERSON>an {
        val objectNames = listOf(GiftMessageContent.VALUE)

        return suspendCancellableCoroutine { cont ->
            RongCoreClient.getInstance().getHistoryMessages(
                conversation.conversationType,
                conversation.targetId,
                objectNames,
                System.currentTimeMillis(),
                100, //最多只加载100条
                RongCommonDefine.GetMessageDirection.FRONT,
                object : IRongCoreCallback.ResultCallback<List<Message>>() {
                    override fun onSuccess(messages: List<Message>?) {

                        val existUnreadReceivedGift = messages?.any {
                            val isReceive = it.messageDirection == MessageDirection.RECEIVE //接收方
                            val isUnread = !it.receivedStatus.isRead //未读
                            val isGift = it.content is GiftMessageContent //礼物消息

                            /**
                             * 满足以下条件
                             * 1. 接收方
                             * 2. 未读
                             * 3. 礼物消息
                             */
                            isReceive && isUnread && isGift
                        } ?: false

                        cont.resumeIfActive(existUnreadReceivedGift)
                    }

                    override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                        cont.resumeIfActive(false)
                    }
                }
            )
        }
    }
}