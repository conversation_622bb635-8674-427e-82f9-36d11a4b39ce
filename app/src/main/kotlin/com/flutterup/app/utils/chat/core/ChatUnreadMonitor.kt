package com.flutterup.app.utils.chat.core

import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.base.utils.resumeIfActive
import com.flutterup.base.utils.resumeWithExceptionIfActive
import com.flutterup.chat.core.ChatException
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.listener.OnReceiveMessageWrapperListener
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import javax.inject.Singleton

interface ChatUnreadMonitor : ChatBasicMonitor {

    val totalMessagesCount: StateFlow<Int>

    val systemMessagesCount: StateFlow<Int>

    val customServiceMessagesCount: StateFlow<Int>

    val totalSystemMessagesCount: StateFlow<Int>

    val userMessagesCount: StateFlow<Int>
}


@Singleton
class ChatUnreadMonitorImpl @Inject constructor(
    private val config: GlobalSettingsMonitor,
    private val appScope: CoroutineScope
) : ChatUnreadMonitor, OnReceiveMessageWrapperListener() {

    private val _totalMessagesCount = MutableStateFlow(0)
    private val _systemMessagesCount = MutableStateFlow(0)
    private val _customServiceMessagesCount = MutableStateFlow(0)
    private val _totalSystemMessagesCount = MutableStateFlow(0)
    private val _userMessagesCount = MutableStateFlow(0)

    override val totalMessagesCount: StateFlow<Int> = _totalMessagesCount.asStateFlow()
    override val systemMessagesCount: StateFlow<Int> = _systemMessagesCount.asStateFlow()
    override val customServiceMessagesCount: StateFlow<Int> = _customServiceMessagesCount.asStateFlow()
    override val totalSystemMessagesCount: StateFlow<Int> = _totalSystemMessagesCount.asStateFlow()
    override val userMessagesCount: StateFlow<Int> = _userMessagesCount.asStateFlow()


    override fun connect() {
        RongCoreClient.addOnReceiveMessageListener(this)
        refreshUnreadCount()
    }

    override fun disconnect() {
        RongCoreClient.removeOnReceiveMessageListener(this)
    }

    override fun onReceivedMessage(
        message: Message?,
        profile: ReceivedProfile?
    ) {
        refreshUnreadCount()
    }

    private fun refreshUnreadCount() {
        appScope.launch {
            _totalMessagesCount.value = getTotalUnreadMessageCount()
            _systemMessagesCount.value = getSystemUnreadMessagesCount()
            _customServiceMessagesCount.value = getCustomServiceUnreadMessagesCount()
            _totalSystemMessagesCount.value = _systemMessagesCount.value + _customServiceMessagesCount.value
            _userMessagesCount.value = _totalMessagesCount.value - _totalSystemMessagesCount.value
        }
    }

    private suspend fun getSystemUnreadMessagesCount(): Int {
        return config.noticeAccount.value?.let { account ->
            suspendCancellableCoroutine { cont ->
                RongCoreClient.getInstance().getUnreadCount(
                    Conversation.ConversationType.SYSTEM,
                    account,
                    object : IRongCoreCallback.ResultCallback<Int>() {
                        override fun onSuccess(t: Int?) {
                            cont.resumeIfActive(t ?: 0)
                        }

                        override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                            cont.resumeIfActive(0)
                        }
                    }
                )
            }
        } ?: 0
    }

    private suspend fun getCustomServiceUnreadMessagesCount(): Int {
        return config.customerServiceAccount.value?.let { account ->
            suspendCancellableCoroutine { cont ->
                RongCoreClient.getInstance().getUnreadCount(
                    Conversation.ConversationType.PRIVATE,
                    account,
                    object : IRongCoreCallback.ResultCallback<Int>() {
                        override fun onSuccess(t: Int?) {
                            cont.resumeIfActive(t ?: 0)
                        }

                        override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                            cont.resumeIfActive(0)
                        }
                    }
                )
            }
        } ?: 0
    }

    private suspend fun getTotalUnreadMessageCount(): Int = suspendCancellableCoroutine { cont ->
        RongIMClient.getInstance().getUnreadCount(
            arrayOf(Conversation.ConversationType.PRIVATE),
            object : RongIMClient.ResultCallback<Int>() {
                override fun onSuccess(total: Int?) {
                    cont.resumeIfActive(total ?: 0)
                }

                override fun onError(e: RongIMClient.ErrorCode?) {
                    cont.resumeWithExceptionIfActive(ChatException(e))
                }
            }
        )
    }
}