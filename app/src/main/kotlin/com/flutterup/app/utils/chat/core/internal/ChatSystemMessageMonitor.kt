package com.flutterup.app.utils.chat.core.internal

import com.flutterup.app.model.SystemMessageEntity
import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.utils.NotificationUtils
import com.flutterup.app.utils.UserMonitor
import com.flutterup.app.utils.chat.core.ChatOnReceivedMessageListener
import com.flutterup.chat.message.content.SystemActionMessageContent
import com.flutterup.network.AppDispatchers
import com.flutterup.network.Dispatcher
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

interface ChatSystemMessageMonitor : ChatOnReceivedMessageListener

@Singleton
class ChatSystemMessageMonitorImpl @Inject constructor(
    private val userMonitor: UserMonitor,
    private val appScope: CoroutineScope,
    private val navCenter: GlobalNavCenter,
    @Dispatcher(AppDispatchers.Main) private val dispatcher: CoroutineDispatcher,
) : ChatSystemMessageMonitor {
    override fun onReceivedMessage(
        message: Message?,
        profile: ReceivedProfile?
    ) {
        if (message == null) return

        val conversationType = message.conversationType
        val content = message.content
        val isOffline = profile?.isOffline == true
        val isSelf = userMonitor.isSelf(message.targetId)

        if (
            conversationType == Conversation.ConversationType.SYSTEM //系统消息
            && content is SystemActionMessageContent //数据同步消息
            && isSelf //是自己
            && !isOffline //不是离线消息
        ) {
            appScope.launch(dispatcher) { // 切换到主线程处理
                handleNoticeMessage(content)
            }
        }
    }

    private fun handleNoticeMessage(content: SystemActionMessageContent) {
        val systemAction = content.get(SystemMessageEntity::class.java) ?: return

        when(systemAction.pushType) {
            SystemMessageEntity.PUSH_TYPE_WINKS_YOU, SystemMessageEntity.PUSH_TYPE_VISITOR -> navCenter.navigate(systemAction)

            else -> {
                NotificationUtils.showNotification(systemAction)
            }
        }
    }
}