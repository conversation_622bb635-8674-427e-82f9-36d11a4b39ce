package com.flutterup.app.utils.chat

import com.flutterup.app.model.UserInfo
import com.flutterup.app.network.ApiService
import com.flutterup.base.utils.applicationEntryPoint
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

object ChatUserCache {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface ChatUserCacheEntryPoint {
        fun apiService(): ApiService
    }

    private val userMap = mutableMapOf<String, UserInfo>()

    private val apiService: ApiService by lazy {
        applicationEntryPoint<ChatUserCacheEntryPoint>().apiService()
    }

    fun getUserInfo(userId: String): UserInfo? {
        return userMap[userId]
    }

    fun putUserInfo(userId: String, userInfo: UserInfo?) {
        if (userInfo == null) {
            userMap.remove(userId)
            return
        }
        userMap[userId] = userInfo
    }

    suspend fun getUserInfoOrFetch(userId: String): UserInfo? {
        return getUserInfo(userId) ?: fetchUserInfo(userId)
    }

    suspend fun fetchUserInfo(userId: String): UserInfo? {
        val result = apiService.getAnotherProfileInfo(userId)
        if (result.isSuccess) {
            putUserInfo(userId, result.data)
            return result.data
        }
        return null
    }
}