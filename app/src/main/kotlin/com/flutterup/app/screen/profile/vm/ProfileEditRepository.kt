package com.flutterup.app.screen.profile.vm

import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.Tag
import com.flutterup.app.model.UpdateProfileRequest
import com.flutterup.app.model.UpdateProfileResult
import com.flutterup.app.network.ApiService
import com.flutterup.base.utils.JsonUtils
import com.flutterup.network.BaseResponse
import java.time.LocalDate
import javax.inject.Inject


class ProfileEditRepository @Inject constructor(
    private val apiService: ApiService,
    private val jsonUtils: JsonUtils,
) {

    suspend fun updateMedia(urlList: List<String>): BaseResponse<UpdateProfileResult> {
        val mediaList = urlList.map { MediaItemEntity(type = MediaItemEntity.TYPE_IMAGE, url = it) }

        val request = UpdateProfileRequest(
            step = UpdateProfileRequest.EDIT_MEDIA,
            mediaList = jsonUtils.toJsonList(mediaList, MediaItemEntity::class.java)
        )
        return apiService.updateProfileInfo(request)
    }

    suspend fun updateNickname(nickname: String): BaseResponse<UpdateProfileResult> {
        val request = UpdateProfileRequest(
            step = UpdateProfileRequest.EDIT_NICKNAME,
            nickName = nickname,
        )
        return apiService.updateProfileInfo(request)
    }

    suspend fun updateDesc(desc: String): BaseResponse<UpdateProfileResult> {
        val request = UpdateProfileRequest(
            step = UpdateProfileRequest.EDIT_DESC,
            sign = desc,
        )
        return apiService.updateProfileInfo(request)
    }

    suspend fun updateBirthday(birthday: LocalDate): BaseResponse<UpdateProfileResult> {
        val request = UpdateProfileRequest(
            step = UpdateProfileRequest.EDIT_BIRTHDAY,
            birthday = birthday.toString()
        )
        return apiService.updateProfileInfo(request)
    }

    suspend fun updateInterests(interests: List<Tag>): BaseResponse<UpdateProfileResult> {
        val request = UpdateProfileRequest(
            step = UpdateProfileRequest.EDIT_INTERESTS,
            tag = interests.map { it.id }.joinToString(",")
        )
        return apiService.updateProfileInfo(request)
    }
}