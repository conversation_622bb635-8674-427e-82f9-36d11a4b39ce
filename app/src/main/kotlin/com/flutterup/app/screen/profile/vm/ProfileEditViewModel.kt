package com.flutterup.app.screen.profile.vm

import com.flutterup.app.model.Gender
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.Tag
import com.flutterup.app.screen.profile.state.ProfileEditUiState
import com.flutterup.app.utils.UserRepository
import com.flutterup.base.BaseRepositoryViewModel
import com.flutterup.base.utils.toLocalDate
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import java.time.LocalDate
import javax.inject.Inject

@HiltViewModel
class ProfileEditViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val editRepository: ProfileEditRepository,
) : BaseRepositoryViewModel() {
    private val _uiState = MutableStateFlow(ProfileEditUiState.fromUserInfo(userRepository.userInfo))

    val uiState: StateFlow<ProfileEditUiState> = _uiState.asStateFlow()

    fun updateMediaToBackend() {
        val mediaList = uiState.value.mediaList.filterIsInstance<ProfileEditUiState.MediaStatus.Success>().map { it.url }
        if (mediaList.isEmpty()) return

        scope.launchWithLoading(
            onLoadingChange = { isLoading ->
                _uiState.update { it.copy(isMediaLoading = isLoading) }
            },

            block = {
                val result = editRepository.updateMedia(mediaList)

                if (result.isSuccess) {
                    userRepository.updateUserInfo(result.data?.userInfo)
                }
            }
        )
    }

    fun updateBirthday(date: LocalDate?) {
        if (date == uiState.value.birthday || date == null) return

        scope.launchWithLoading(
            onLoadingChange = { isLoading ->
                _uiState.update { it.copy(isBirthdayLoading = isLoading) }
            },
            block = {
                val result = editRepository.updateBirthday(date)

                if (result.isSuccess) {
                    val userInfo = result.data?.userInfo
                    userRepository.updateUserInfo(userInfo)
                    _uiState.update { it.copy(birthday = date, age = userInfo?.age.toString()) }
                }
            }
        )
    }

    fun updateNickname(nickname: String) {
        if (nickname == uiState.value.nickname || nickname.isEmpty()) return

        scope.launchWithLoading(
            onLoadingChange = { isLoading ->
                _uiState.update { it.copy(isNicknameLoading = isLoading) }
            },
            block = {
                val result = editRepository.updateNickname(nickname)

                if (result.isSuccess) {
                    userRepository.updateUserInfo(result.data?.userInfo)
                    _uiState.update { it.copy(nickname = nickname) }
                }
            }
        )
    }

    fun updateInterests(currentInterests: List<Tag>) {
        if (currentInterests.isEmpty()) return
        if (currentInterests == uiState.value.interests) return

        scope.launchWithLoading(
            onLoadingChange = { isLoading ->
                _uiState.update { it.copy(isInterestsLoading = isLoading) }
            },
            block = {
                val result = editRepository.updateInterests(currentInterests)

                if (result.isSuccess) {
                    userRepository.updateUserInfo(result.data?.userInfo)
                    _uiState.update { it.copy(interests = currentInterests.map { interest -> interest.title.orEmpty() }) }
                }
            }
        )
    }

    fun updateDesc(desc: String) {
        if (desc == uiState.value.desc) return //简介可以重设为空，不过滤

        scope.launchWithLoading(
            onLoadingChange = { isLoading ->
                _uiState.update { it.copy(isDescLoading = isLoading) }
            },
            block = {
                val result = editRepository.updateDesc(desc)

                if (result.isSuccess) {
                    userRepository.updateUserInfo(result.data?.userInfo)
                    _uiState.update { it.copy(desc = desc) }
                }
            }
        )
    }

    fun addMedia(index: Int, url: String) {
        _uiState.update { uiState ->
            val successStatus = ProfileEditUiState.MediaStatus.Success(url)
            val mediaList = uiState.mediaList.toMutableList().also { it[index] = successStatus }
            uiState.copy(mediaList = mediaList)
        }
    }

    fun startMediaLoading(index: Int) {
        _uiState.update { uiState ->
            val mediaList = uiState.mediaList.toMutableList().also { it[index] = ProfileEditUiState.MediaStatus.Loading }
            uiState.copy(mediaList = mediaList)
        }
    }

    fun endMediaLoading(index: Int) {
        val status = _uiState.value.mediaList.getOrNull(index)

        if (status !is ProfileEditUiState.MediaStatus.Success) {
            _uiState.update { uiState ->
                val mediaList = uiState.mediaList.toMutableList().also { it[index] = ProfileEditUiState.MediaStatus.Idle }
                uiState.copy(mediaList = mediaList)
            }
        }
    }

    /**
     * 更新本地media，上传到后台需要用户手动点save
     */
    fun updateMedia(index: Int, url: String) {
        _uiState.update { uiState ->
            val successStatus = ProfileEditUiState.MediaStatus.Success(url)
            val mediaList = uiState.mediaList.toMutableList().also { it[index] = successStatus }
            uiState.copy(mediaList = mediaList)
        }
    }

    fun deleteMedia(occupiedIndex: Int) {
        _uiState.update { uiState ->
            val mediaList = uiState.mediaList.toMutableList()

            mediaList.removeAt(occupiedIndex) //删除
            mediaList.add(ProfileEditUiState.MediaStatus.Idle) //添加到最后
            uiState.copy(mediaList = mediaList)
        }
    }
}