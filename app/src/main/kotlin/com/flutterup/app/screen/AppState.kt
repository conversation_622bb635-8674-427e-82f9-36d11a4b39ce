package com.flutterup.app.screen

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.navigation.NavDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.flutterup.app.model.ConfigSettingsEntity
import com.flutterup.app.model.UserCountEntity
import com.flutterup.app.navigation.BottomNavRoute
import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.navigation.TopLevelDestination
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.UserOnlineMonitor
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.app.utils.chat.ChatMonitor
import com.flutterup.app.utils.chat.core.ChatUnreadMonitor
import com.flutterup.network.impl.NetworkMonitor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn


private val NavHostController.canGoBack: Boolean get() = this.previousBackStackEntry != null

@Composable
fun rememberAppState(
    networkMonitor: NetworkMonitor,
    relateMonitor: UserUnreadMonitor,
    settingsMonitor: GlobalSettingsMonitor,
    chatMonitor: ChatMonitor,
    onlineMonitor: UserOnlineMonitor,
    navCenter: GlobalNavCenter,
    navController: NavHostController = rememberNavController(),
    coroutineScope: CoroutineScope = rememberCoroutineScope(),
): AppState {

    // We want to preserve the app state across config changes
    // We only need to initialize AppState once, and don't want to re-create it when the
    // Compose environment is recreated (e.g. due to configuration change).
    return remember(networkMonitor, relateMonitor, settingsMonitor, chatMonitor, onlineMonitor, navCenter, navController, coroutineScope) {
        AppState(
            networkMonitor = networkMonitor,
            relateMonitor = relateMonitor,
            settingsMonitor = settingsMonitor,
            chatMonitor = chatMonitor,
            onlineMonitor = onlineMonitor,
            navCenter = navCenter,
            navController = navController,
            coroutineScope = coroutineScope,
        )
    }
}

@Stable
class AppState(
    val relateMonitor: UserUnreadMonitor,
    val networkMonitor: NetworkMonitor,
    val settingsMonitor: GlobalSettingsMonitor,
    val chatMonitor: ChatMonitor,
    onlineMonitor: UserOnlineMonitor,
    val navCenter: GlobalNavCenter,
    val navController: NavHostController,
    coroutineScope: CoroutineScope,
) {
    init {
        navCenter.setNavController(navController) //更新导航控制器
    }

    val currentDestination: NavDestination? @Composable get() = navController.findCurrentDestination()

    val isOnline = networkMonitor.isOnline
        .map(Boolean::not)
        .stateIn(
            coroutineScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )

    val relateUnreadCount = relateMonitor.unreadCount
        .stateIn(
            coroutineScope,
            started = SharingStarted.WhileSubscribed(1000),
            initialValue = UserCountEntity.EMPTY
        )

    val chatUnreadCount = chatMonitor.unreadMonitor.totalMessagesCount
        .stateIn(
            coroutineScope,
            started = SharingStarted.Eagerly,
            initialValue = 0
        )

    val settings = settingsMonitor.settings
        .stateIn(
            coroutineScope,
            started = SharingStarted.Eagerly,
            initialValue = ConfigSettingsEntity()
        )

    val onlineUsers = onlineMonitor.onlineUserList
        .stateIn(
            coroutineScope,
            started = SharingStarted.Eagerly,
            initialValue = emptyList()
        )

    /**
     * Map of top level destinations to be used in the TopBar, BottomBar and NavRail. The key is the
     * route.
     */
    val topLevelDestinations: List<TopLevelDestination> = TopLevelDestination.entries

    /**
     * UI logic for navigating to a top level destination in the app. Top level destinations have
     * only one copy of the destination of the back stack, and save and restore state whenever you
     * navigate to and from it.
     *
     * @param topLevelDestination: The destination the app needs to navigate to.
     */
    fun navigateToTopLevelDestination(topLevelDestination: TopLevelDestination) {
        navCenter.navigateToTopLevelDestination(topLevelDestination)
    }

    fun navigateToTopLevelDestination(route: BottomNavRoute) {
        navCenter.navigateToTopLevelDestination(route)
    }


    fun canGoBack(): Boolean {
        return navController.canGoBack
    }

    @Composable
    private fun NavHostController.findCurrentDestination(): NavDestination? {
        val navBackStackEntry by currentBackStackEntryAsState()
        return navBackStackEntry?.destination
    }
}
