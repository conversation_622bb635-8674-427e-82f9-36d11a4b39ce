@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Badge
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.BuildConfig
import com.flutterup.app.R
import com.flutterup.app.design.component.AppAvatar
import com.flutterup.app.design.component.AppBackground
import com.flutterup.app.design.component.AppLineOptionText
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.preview.BooleanProvider
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.RedPrimary
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.PaymentPacksOrderBy
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.common.HelperCenterRoute
import com.flutterup.app.screen.common.WebViewRoute
import com.flutterup.app.screen.payment.PaymentDiamondsRoute
import com.flutterup.app.screen.payment.PaymentPacksRoute
import com.flutterup.app.screen.payment.PaymentSubscriptionRoute
import com.flutterup.app.screen.profile.modifier.drawPacksLabel
import com.flutterup.app.screen.profile.state.ProfileHomeOptions
import com.flutterup.app.screen.profile.state.ProfileHomeOptions.ChildProtection
import com.flutterup.app.screen.profile.state.ProfileHomeOptions.ClearCache
import com.flutterup.app.screen.profile.state.ProfileHomeOptions.HelpCenter
import com.flutterup.app.screen.profile.state.ProfileHomeOptions.PrivatePolicy
import com.flutterup.app.screen.profile.state.ProfileHomeOptions.Settings
import com.flutterup.app.screen.profile.state.ProfileHomeOptions.TermsPolicy
import com.flutterup.app.screen.profile.state.ProfileHomeOptions.Version
import com.flutterup.app.screen.profile.state.ProfilePacks
import com.flutterup.app.screen.profile.state.ProfilePacksPhoto
import com.flutterup.app.screen.profile.state.ProfilePacksPingChat
import com.flutterup.app.screen.profile.state.ProfilePacksVideo
import com.flutterup.app.screen.profile.state.ProfileUiState
import com.flutterup.app.screen.profile.vm.ProfileHomeViewModel
import com.flutterup.app.screen.settings.SettingsBaseRoute
import com.flutterup.app.utils.PlayStoreUtils
import dev.chrisbanes.haze.HazeStyle
import dev.chrisbanes.haze.hazeEffect

@Composable
fun ProfileScreen() {
    val navController = LocalNavController.current
    val appState = LocalAppState.current
    val viewModel: ProfileHomeViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    val termsTitle = stringResource(R.string.terms_policy)
    val privacyTitle = stringResource(R.string.privacy_policy)
    val childTitle = stringResource(R.string.child_policy)

    val terms by appState.settingsMonitor.terms.collectAsStateWithLifecycle(null)
    val privacy by appState.settingsMonitor.privacy.collectAsStateWithLifecycle(null)
    val childSafe by appState.settingsMonitor.childSafe.collectAsStateWithLifecycle(null)


    ProfileContent(
        uiState = uiState,
        onProfileEditClick = {
            navController.navigate(ProfileEditRoute)
        },
        onSubscriptionClick = {
            val eventFrom = if (uiState.isVip) {
                AppPaymentFrom.PROFILE_VIP
            } else {
                AppPaymentFrom.PROFILE_NO_VIP
            }
            navController.navigate(PaymentSubscriptionRoute(eventFrom))
        },
        onPacksItemClick = {
            val orderBy = when(it) {
                is ProfilePacksPhoto -> PaymentPacksOrderBy.NORMAL
                is ProfilePacksPingChat -> PaymentPacksOrderBy.PING_CHAT_FIRST
                is ProfilePacksVideo -> PaymentPacksOrderBy.VIDEOS_FIRST
            }

            navController.navigate(PaymentPacksRoute(orderBy))
        },
        onBuyDiamondClick = {
            navController.navigate(PaymentDiamondsRoute)
        },
        onProfileOptionsClick = {
            when(it) {
                Settings -> navController.navigate(SettingsBaseRoute)
                HelpCenter -> navController.navigate(HelperCenterRoute)
                PrivatePolicy -> privacy?.let { url ->
                    navController.navigate(WebViewRoute(
                        title = privacyTitle,
                        url = url
                    ))
                }
                TermsPolicy -> terms?.let { url ->
                    navController.navigate(WebViewRoute(
                        title = termsTitle,
                        url = url
                    ))
                }
                ChildProtection -> childSafe?.let { url ->
                    navController.navigate(WebViewRoute(
                        title = childTitle,
                        url = url
                    ))
                }
                ClearCache -> viewModel.cleanCache()
                Version -> {
                    if (uiState.existNewVersion) {
                        PlayStoreUtils.gotoPlayStore()
                    }
                }
            }
        },
        onStealthModeClick = {
            TODO()
        }
    )
}

@Composable
private fun ProfileContent(
    uiState: ProfileUiState,
    onProfileEditClick: () -> Unit = {},
    onSubscriptionClick: () -> Unit = {},
    onPacksItemClick: (ProfilePacks) -> Unit = {},
    onBuyDiamondClick: () -> Unit = {},
    onProfileOptionsClick: (ProfileHomeOptions) -> Unit = {},
    onStealthModeClick: () -> Unit = {},
) {
    val statusBarPaddingValues = WindowInsets.systemBars.asPaddingValues()

    AppBackground {
        Column(
            Modifier
                .padding()
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(247.dp)
            ) {
                Image(
                    painter = painterResource(R.mipmap.ic_profile_top_bg),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.matchParentSize()
                )

                ProfileHomeHeader(
                    paddingValues = PaddingValues(top = statusBarPaddingValues.calculateTopPadding() + 20.dp),
                    uiState = uiState,
                    onProfileEditClick = onProfileEditClick,
                )

                ProfileStealthMode(
                    uiState = uiState,
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(end = 15.dp, top = statusBarPaddingValues.calculateTopPadding()),
                    onClick = onStealthModeClick,
                )
            }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .offset(y = (-72).dp)
            ) {
                Image(
                    painter = painterResource(R.mipmap.ic_profile_vip_status),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .fillMaxWidth()
                        .height(90.dp)
                        .noRippleClickable(onClick = onSubscriptionClick)
                )

                Spacer(modifier = Modifier.height(10.dp))

                ProfileHomePacks(
                    uiState = uiState,
                    onPacksItemClick = onPacksItemClick,
                    onBuyDiamondClick = onBuyDiamondClick,
                )

                Spacer(modifier = Modifier.height(12.dp))

                ProfileHomeSettings(
                    uiState = uiState,
                    onProfileOptionsClick = onProfileOptionsClick,
                )
            }
        }
    }
}

@Composable
private fun ProfileStealthMode(
    uiState: ProfileUiState,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    Row(
        modifier = modifier
            .size(width = 83.dp, height = 18.dp)
            .background(color = Color(0x33310029), shape = RoundedCornerShape(4.dp))
            .noRippleClickable(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Icon(
            painter = painterResource(
                if (uiState.isPrivateExplorer) {
                    R.mipmap.ic_prrivate_explorer_on
                } else {
                    R.mipmap.ic_prrivate_explorer_off
                }
            ),
            contentDescription = null,
            tint = Color.Unspecified,
            modifier = Modifier.size(12.dp)
        )

        Spacer(modifier = Modifier.width(2.dp))

        Text(
            text = stringResource(R.string.stealth_mode),
            style = TextStyle(
                fontSize = 10.sp,
                lineHeight = 10.sp,
                fontWeight = FontWeight.W400,
                color = Color.White,
            )
        )
    }
}

@Composable
private fun ProfileHomeHeader(
    paddingValues: PaddingValues,
    uiState: ProfileUiState,
    onProfileEditClick: () -> Unit = {},
) {
    val visitorModifier = Modifier
        .clip(CircleShape)
        .border(width = 0.5.dp, color = Color.White, shape = CircleShape)
        .padding(0.5.dp)
        .size(17.dp)
        .hazeEffect(
            HazeStyle(
                tint = null,
                blurRadius = 40.dp,
                noiseFactor = 0.15f,
            )
        )

    val normalModifier = Modifier.border(width = 2.dp, color = Color.White.copy(0.4f), shape = CircleShape)
    val vipModifier = Modifier.border(
        width = 2.dp,
        shape = CircleShape,
        brush = Brush.verticalGradient(
            colors = listOf(
                Color(0xFFC37066),
                Color(0xFFF2C9B3),
            ),
        )
    )

    Row(
        modifier = Modifier.padding(start = 30.dp, top = paddingValues.calculateTopPadding()),
        verticalAlignment = Alignment.CenterVertically
    ) {
        AppAvatar(
            modifier = Modifier.noRippleClickable(onClick = onProfileEditClick)
        ) {
            AsyncImage(
                model = uiState.headimg,
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .clip(CircleShape)
                    .then(if (uiState.isVip) vipModifier else normalModifier)
                    .padding(2.dp)
                    .width(80.dp)
                    .height(80.dp)
            )

            if (uiState.isVip) {
                Image(
                    painter = painterResource(R.mipmap.ic_label_vip),
                    contentDescription = null,
                    modifier = Modifier
                        .size(22.dp)
                        .align(Alignment.TopEnd)
                )
            }



            Row(
                modifier = Modifier
                    .width(47.dp)
                    .height(16.dp)
                    .background(color = Color(0x66310029), shape = RoundedCornerShape(size = 8.dp))
                    .align(Alignment.BottomCenter),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_profile_edit),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier.size(10.83.dp)
                )

                Spacer(Modifier.width(3.dp))
                
                Text(
                    text = stringResource(R.string.edit),
                    style = TextStyle(
                        fontSize = 10.sp,
                        lineHeight = 10.sp,
                        fontWeight = FontWeight.W400,
                        color = Color.White,
                    )
                )
            }
        }

        Spacer(modifier = Modifier.width(8.dp))

        Column {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Spacer(modifier = Modifier.width(4.dp))

                Text(
                    text = uiState.nickname.orEmpty(),
                    style = TextStyle(
                        fontSize = 26.sp,
                        lineHeight = 20.sp,
                        fontWeight = FontWeight.W900,
                        color = Color.White,
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.padding(end = 70.dp)
                )
            }

            if (uiState.newVisitorsHeadimg.isNotEmpty()) {
                Spacer(modifier = Modifier.height(15.dp))

                Row (
                    modifier = Modifier
                        .height(28.dp)
                        .background(
                            color = Color(0x36310029),
                            shape = RoundedCornerShape(20.dp)
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Spacer(modifier = Modifier.width(6.dp))

                    Badge(
                        modifier = Modifier.size(3.dp),
                        contentColor = RedPrimary
                    )

                    Spacer(modifier = Modifier.width(4.dp))

                    Text(
                        text = stringResource(R.string.profile_new_visitor),
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 12.sp,
                            fontWeight = FontWeight.W500,
                            color = Color.White,
                        )
                    )

                    Spacer(modifier = Modifier.width(13.dp))

                    //最多3个
                    repeat(uiState.newVisitorsHeadimg.size.coerceAtMost(3)) { index ->
                        AsyncImage(
                            modifier = Modifier
                                .offset((-index * 8.5f).dp)
                                .zIndex(-index.toFloat())
                                .then(visitorModifier),
                            model = uiState.newVisitorsHeadimg[index],
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ProfileHomeSettings(
    uiState: ProfileUiState,
    onProfileOptionsClick: (ProfileHomeOptions) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(10.dp),
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(bottom = 20.dp)
    ) {
        repeat(uiState.options.size) {
            val option = uiState.options[it]

            AppLineOptionText(
                title = stringResource(option.titleRes),
                iconDrawableRes = option.iconRes,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp)
                    .noRippleClickable {
                        onProfileOptionsClick(option)
                    },
                rightContent = {
                    when(option) {
                        ClearCache -> {
                            Text(
                                text = uiState.cache,
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.W400,
                                    color = TextGray999,
                                ),
                            )
                        }
                        Version -> {
                            Box {
                                Text(
                                    text = "v${BuildConfig.VERSION_NAME}",
                                    style = TextStyle(
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.W400,
                                        color = TextGray999,
                                    ),
                                )

                                if (uiState.existNewVersion) {
                                    Badge(
                                        containerColor = Color.Red,
                                        contentColor = Color.White,
                                        modifier = Modifier
                                            .offset(x = 3.dp)
                                            .align(Alignment.TopEnd)
                                    )
                                }
                            }
                        }
                        else -> {
                            Image(
                                painter = painterResource(R.drawable.ic_right_purple_arrow),
                                contentDescription = null,
                            )
                        }
                    }
                }
            )
        }
    }
}

@Composable
private fun ProfileHomePacks(
    uiState: ProfileUiState,
    onPacksItemClick: (ProfilePacks) -> Unit = {},
    onBuyDiamondClick: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .padding(horizontal = 15.dp)
            .fillMaxWidth()
            .background(brush = PACKS_BACKGROUND, shape = RoundedCornerShape(12.dp))
    ){
        Row(
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .padding(start = 3.dp, end = 3.dp, top = 3.dp)
                .fillMaxWidth()
                .height(100.dp)
                .background(brush = PACKS_INNER_BACKGROUND, shape = RoundedCornerShape(12.dp))
        ) {
            repeat(uiState.packs.size) {
                val packs = uiState.packs[it]

                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight()
                        .noRippleClickable {
                            onPacksItemClick(packs)
                        },
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box {
                        Image(
                            painter = painterResource(packs.iconRes),
                            contentDescription = null,
                            modifier = Modifier.size(45.5f.dp)
                        )

                        Text(
                            text = "+${packs.count}",
                            style = TextStyle(
                                fontSize = 12.sp,
                                lineHeight = 10.sp,
                                fontWeight = FontWeight(600),
                                fontStyle = FontStyle.Italic,
                                color = Color.White,
                                textAlign = TextAlign.Center
                            ),
                            modifier = Modifier
                                .offset(x = 22.5.dp, y = (-8.5).dp)
                                .drawPacksLabel()
                                .padding(horizontal = 7.dp, vertical = 2.dp)
                                .defaultMinSize(minWidth = 30.dp)
                                .align(Alignment.TopEnd),
                        )
                    }

                    Spacer(Modifier.height(5.dp))

                    Text(
                        text = stringResource(packs.titleRes),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W600,
                            color = TextBlack666,
                        )
                    )
                }
            }
        }

        Row(
            modifier = Modifier.padding(start = 13.dp, end = 11.dp, top = 11.dp, bottom = 7.dp),
            verticalAlignment = Alignment.Bottom
        ) {
            Text(
                text = stringResource(R.string.diamond_balance),
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFFFFFFFF),
                )
            )

            Spacer(modifier = Modifier.width(6.dp))

            Text(
                text = uiState.diamond.toString(),
                style = TextStyle(
                    fontSize = 25.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight.W900,
                    color = Color.White,
                )
            )

            Spacer(modifier = Modifier.weight(1f))

            Box(
                Modifier
                    .border(
                        width = 1.dp,
                        color = Color.White,
                        shape = RoundedCornerShape(size = 21.dp)
                    )
                    .width(74.dp)
                    .height(20.dp)
                    .noRippleClickable(onClick = onBuyDiamondClick),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(R.string.add_funds),
                    style = TextStyle(
                        fontSize = 12.sp,
                        lineHeight = 22.sp,
                        fontWeight = FontWeight.W400,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    ),
                )
            }
        }
    }
}

private val PACKS_GRADIENT_START = Color(0xFFBE93E7)
private val PACKS_GRADIENT_END = Color(0xFFE8BDFF)
private val PACKS_BACKGROUND = Brush.horizontalGradient(
    colors = listOf(
        PACKS_GRADIENT_START,
        PACKS_GRADIENT_END
    )
)

private val PACKS_INNER_BACKGROUND = Brush.verticalGradient(
    listOf(
        Color.White,
        Color.White.copy(0.57f)
    )
)


@Preview
@Composable
private fun ProfileScreenPreview(
    @PreviewParameter(BooleanProvider::class) isAly: Boolean,
) {
    AppTheme {
        ProfileContent(uiState = ProfileUiState(
            nickname = "Wendy Wulawulawula",
            headimg = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
            newVisitorsCount = 10,
            newVisitorsHeadimg = listOf(
                "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"
            ),
            isVip = true,
            isAly = isAly,
            privateExplorer = true
        ))
    }
}