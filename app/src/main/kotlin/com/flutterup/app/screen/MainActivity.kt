package com.flutterup.app.screen

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.lifecycleScope
import com.flutterup.app.design.component.AppBackground
import com.flutterup.app.navigation.AppScreen
import com.flutterup.base.compose.BaseComposeActivity
import com.flutterup.network.impl.NetworkMonitor
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.navigation.GlobalNavConst
import com.flutterup.app.screen.common.MediaPreviewRoute
import com.flutterup.app.utils.AppStatus
import com.flutterup.app.utils.AppStatusMonitor
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.UserOnlineMonitor
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.app.utils.chat.ChatMonitor
import com.flutterup.base.BaseActivity
import com.flutterup.base.permission.PermissionManager
import com.flutterup.base.utils.JsonUtils
import com.flutterup.network.Action
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : BaseActivity() {
    @Inject lateinit var permissionManager: PermissionManager
    @Inject lateinit var appStatusMonitor: AppStatusMonitor
    @Inject lateinit var networkMonitor: NetworkMonitor
    @Inject lateinit var relateMonitor: UserUnreadMonitor
    @Inject lateinit var settingsMonitor: GlobalSettingsMonitor
    @Inject lateinit var chatMonitor: ChatMonitor
    @Inject lateinit var onlineMonitor: UserOnlineMonitor
    @Inject lateinit var navCenter: GlobalNavCenter
    @Inject lateinit var jsonUtils: JsonUtils

    private val viewModel: MainViewModel by viewModels()

    private val globalViewModel: GlobalViewModel by viewModels()

    override fun onResume() {
        super.onResume()
        onlineMonitor.start()
        appStatusMonitor.onAppStatusChangedSync(AppStatus.FOREGROUND)
    }

    override fun onPause() {
        super.onPause()
        onlineMonitor.cancel()
        appStatusMonitor.onAppStatusChangedSync(AppStatus.BACKGROUND)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val splashScreen = installSplashScreen()
        super.onCreate(savedInstanceState)

        // 控制启动页显示时机
        splashScreen.setKeepOnScreenCondition {
            val uiState = viewModel.uiState.value
            uiState.shouldKeepSplashScreen()
        }

        catchIntentAction(intent)
        setContent { Screen() }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        catchIntentAction(intent)
    }

    @Composable
    private fun Screen() {
        val appState = rememberAppState(
            networkMonitor = networkMonitor,
            relateMonitor = relateMonitor,
            settingsMonitor = settingsMonitor,
            chatMonitor = chatMonitor,
            onlineMonitor = onlineMonitor,
            navCenter = navCenter
        )
        val uiState by viewModel.uiState.collectAsState()

        CompositionLocalProvider(
            LocalAppState provides appState,
            LocalNavController provides appState.navController,
            LocalPermissionManager provides permissionManager,
        ) {
            AppTheme {
                AppBackground {
                    AppScreen(uiState)
                }
            }
        }
    }

    private fun catchIntentAction(intent: Intent?) {
        if (intent == null) return

        with(intent.data?.getQueryParameter(GlobalNavConst.KEY_ACTION)) {
            if (this != null) {
                val action = jsonUtils.fromJson(this, Action::class.java) ?: return
                navCenter.navigate(action)
                return
            }
        }

        with(intent.getParcelableExtra<Action>(GlobalNavConst.KEY_ACTION)) {
            if (this != null) {
                navCenter.navigate(this)
                return
            }
        }

        with(intent.getStringExtra(GlobalNavConst.KEY_ACTION)) {
            if (this != null) {
                val action = jsonUtils.fromJson(this, Action::class.java) ?: return
                navCenter.navigate(action)
                return
            }
        }
    }
}