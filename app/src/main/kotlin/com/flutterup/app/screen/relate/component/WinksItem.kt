@file:OptIn(ExperimentalHazeMaterialsApi::class)

package com.flutterup.app.screen.relate.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.component.AppAvatar
import com.flutterup.app.design.component.AppNearby
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.preview.WinkTypeProvider
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.TextBlack31
import com.flutterup.app.design.theme.WinkBorder
import com.flutterup.app.design.theme.WinkGradientEnd
import com.flutterup.app.design.theme.WinkGradientStart
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.WinkItemEntity
import com.flutterup.app.model.WinkType
import dev.chrisbanes.haze.HazeStyle
import dev.chrisbanes.haze.HazeTint
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.materials.ExperimentalHazeMaterialsApi
import dev.chrisbanes.haze.materials.HazeMaterials
import dev.chrisbanes.haze.rememberHazeState

@Composable
fun WinksItem(
    winkType: WinkType,
    item: WinkItemEntity,
    modifier: Modifier = Modifier,
    blurEnabled: Boolean = true,
    shape: Shape = RoundedCornerShape(20.dp),
    mediaShape: Shape = RoundedCornerShape(9.dp),
    contentBlurRadius: Dp = 4.dp,
    mediaBlurRadius: Dp = 40.dp,
    onAvatarClick: () -> Unit = {},
    onDislikeClick: () -> Unit = {},
    onLikeClick: () -> Unit = {},
    onBottomClick: () -> Unit = {},
) {
    val borderModifier = if (item.read == 0 && winkType == WinkType.Received) {
        Modifier.border(1.dp, brush = Brush.linearGradient(
            colors = listOf(
                Color(0xFFC8AEF6),
                Color(0xFFC9AFF6),
            )
        ), shape)
    } else {
        Modifier
    }

    val haze = rememberHazeState(blurEnabled = true)
    val text = when(winkType) {
        WinkType.Received -> stringResource(R.string.relate_ago_text, item.timeout.orEmpty())
        WinkType.Sent -> stringResource(R.string.relate_ping_chat_now)
    }

    val contentForegroundModifier = if (blurEnabled) {
        Modifier.hazeEffect(style = HazeStyle(
            tint = null,
            blurRadius = contentBlurRadius,
            noiseFactor = 0.15f,
        ))
    } else {
        Modifier
    }

    val mediaForegroundModifier = if (blurEnabled) {
        Modifier.hazeEffect(style = HazeStyle(
            tint = null,
            blurRadius = mediaBlurRadius,
            noiseFactor = 0.15f,
        ))
    } else {
        Modifier
    }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .defaultMinSize(minHeight = 176.dp)
            .then(borderModifier),
        shape = shape,
        colors = CardDefaults.cardColors(
            containerColor = Color.White,
            contentColor = Color.Unspecified,
        )
    ) {
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column {
                Row(
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 8.dp, top = 8.dp, end = 24.dp)
                ) {
                    Row(
                        modifier = Modifier.noRippleClickable(onClick = onAvatarClick),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        AppAvatar(
                            online = item.online == 1
                        ) {
                            AsyncImage(
                                model = item.headimg,
                                contentDescription = null,
                                contentScale = ContentScale.Crop,
                                modifier = Modifier
                                    .size(40.dp)
                                    .clip(CircleShape)
                                    .then(contentForegroundModifier)
                            )
                        }

                        Spacer(Modifier.width(9.dp))

                        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(15.dp),
                                modifier = Modifier.then(contentForegroundModifier)
                            ) {
                                Text(
                                    text = item.nickname.orEmpty(),
                                    style = TextStyle(
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.W900,
                                        color = TextBlack31,
                                    ),
                                )

                                Text(
                                    text = item.age.toString(),
                                    style = TextStyle(
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.W900,
                                        color = TextBlack31,
                                    )
                                )
                            }

                            if (item.nearby == 1) {
                                AppNearby()
                            }
                        }
                    }

                    //只有接收才有dislike，like
                    if (winkType == WinkType.Received) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                painter = painterResource(R.mipmap.ic_relate_dislike),
                                contentDescription = null,
                                modifier = Modifier
                                    .noRippleClickable(onClick = onDislikeClick)
                                    .size(width = 46.dp, height = 28.dp)
                            )

                            Spacer(Modifier.width(11.dp))

                            Image(
                                painter = painterResource(R.mipmap.ic_relate_like),
                                contentDescription = null,
                                modifier = Modifier
                                    .noRippleClickable(onClick = onLikeClick)
                                    .size(width = 46.dp, height = 28.dp)
                            )
                        }
                    }
                }

                Row(
                    modifier = Modifier
                        .padding(start = 54.dp, top = 6.dp, end = 12.dp, bottom = 6.dp)
                        .hazeSource(haze)
                ) {
                    //最多只会显示3个
                    val size = (item.mediaList?.size ?: 0).coerceAtMost(3)
                    repeat(size) {
                        val media = item.mediaList?.get(it) ?: return@repeat

                        AsyncImage(
                            model = media.imageUrl,
                            contentDescription = null,
                            contentScale = ContentScale.FillBounds,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(3f / 4f)
                                .padding(7.dp)
                                .clip(mediaShape)
                                .then(mediaForegroundModifier)
                        )
                    }
                }
            }

            Box(
                modifier = Modifier
                    .padding(start = 8.dp, end = 8.dp, bottom = 4.dp)
                    .fillMaxWidth()
                    .height(27.dp)
                    .clip(shape)
                    .hazeEffect(haze, HazeMaterials.ultraThin())
                    .background(
                        brush = BOTTOM_BRUSH,
                        shape = shape
                    )
                    .border(
                        width = 1.dp,
                        color = WinkBorder,
                        shape = shape
                    )
                    .align(Alignment.BottomCenter)
                    .noRippleClickable(onClick = onBottomClick),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = text,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W300,
                        color = Color.White.copy(alpha = 0.7f),
                    )
                )
            }
        }
    }
}

private val BOTTOM_BRUSH = Brush.horizontalGradient(
    colors = listOf(
        WinkGradientStart,
        WinkGradientEnd
    )
)

@Preview
@Composable
private fun WinksItemPreview(
    @PreviewParameter(WinkTypeProvider ::class) winkType: WinkType,
) {
    AppTheme {
        WinksItem(
            winkType = winkType,
            item = WinkItemEntity(
                id = 1,
                userId = "1",
                nickname = "test",
                mediaList = listOf(
                    MediaItemEntity(
                        type = 0,
                        url = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"
                    ),
                    MediaItemEntity(
                        type = 0,
                        url = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"
                    ),
                    MediaItemEntity(
                        type = 0,
                        url = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"
                    )
                ),
                age = 18,
                nearby = 1,
                location = 0,
                online = 1,
                timeout = "10:00",
                read = 0,
                headimg = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg"
            )
        )
    }
}