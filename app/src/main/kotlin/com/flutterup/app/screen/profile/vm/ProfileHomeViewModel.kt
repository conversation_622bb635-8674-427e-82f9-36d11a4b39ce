package com.flutterup.app.screen.profile.vm

import android.content.Context
import coil3.imageLoader
import com.flutterup.app.R
import com.flutterup.app.screen.GlobalRepository
import com.flutterup.app.screen.profile.state.ProfileUiState
import com.flutterup.app.utils.UserRepository
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.base.AppDirs
import com.flutterup.base.BaseRepository
import com.flutterup.base.BaseRepositoryViewModel
import com.flutterup.base.Dirs
import com.flutterup.base.utils.FileUtils
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@HiltViewModel
class ProfileHomeViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val unreadMonitor: UserUnreadMonitor,
    private val repository: ProfileRepository,
    private val globalRepository: GlobalRepository,
    @ApplicationContext private val context: Context,
    @Dirs(AppDirs.APP_CACHE) private val cacheDir: File,
) : BaseRepositoryViewModel() {

    private val _uiState: MutableStateFlow<ProfileUiState> = MutableStateFlow(ProfileUiState())


    val uiState = combine(
        _uiState,
        unreadMonitor.unreadCount,
        userRepository.userInfoState
    ) { ui, unread, userInfo ->
        ui.copy(
            newVisitorsCount = unread.visitorNewNum,
            newVisitorsHeadimg = unread.headList,

            isAly = userInfo?.isAly() ?: false,
            nickname = userInfo?.nickname,
            headimg = userInfo?.headImage,
            isVip = userInfo?.right?.vip == 1,
            pp = userInfo?.right?.privacyImage ?: 0,
            pv = userInfo?.right?.privacyVideo ?: 0,
            pingchat = userInfo?.right?.flashChat ?: 0,
            diamond = 0.00, //TODO 修改为真实的钻石

            cache = ui.cache,
            existNewVersion = ui.existNewVersion,
            isCleanCaching = ui.isCleanCaching,
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    init {
        refresh()
    }

    fun refresh() {
        getProfileInfo()
        getAppCacheFileSize()
        getAppUpgradeInfo()
    }

    fun cleanCache() {
        scope.launch(Dispatchers.IO) {
            _uiState.update { it.copy(isCleanCaching = true) }
            //清空缓存
            cacheDir.deleteRecursively()
            //更新下缓存大小
            getAppCacheFileSize()
        }.invokeOnCompletion {
            _uiState.update { it.copy(isCleanCaching = false) }
            Timber.showToast(context.getString(R.string.cache_cleared))
        }
    }

    private fun getProfileInfo() {
        scope.launch {
            val result = repository.getMineProfileInfo()
            userRepository.updateUserInfo(result)
        }
    }

    private fun getAppUpgradeInfo() {
        scope.launch {
            val result = globalRepository.checkAppUpgrade()
            //返回了版本信息说明存在新版本
            _uiState.update { it.copy(existNewVersion = result?.version != null) }
        }
    }

    private fun getAppCacheFileSize() {
        scope.launch {
            val cacheSize = FileUtils.getFolderSize(cacheDir)
            val cacheSizeStr = FileUtils.formatFileSize(cacheSize)
            _uiState.update { it.copy(cache = cacheSizeStr) }
        }
    }
}