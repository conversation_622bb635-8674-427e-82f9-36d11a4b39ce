package com.flutterup.app.screen.chat

import androidx.compose.runtime.rememberCoroutineScope
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import com.flutterup.app.navigation.BottomNavRoute
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

@Serializable data object ChatRoute : BottomNavRoute

@Serializable data object ChatSystemRoute

@Serializable data object PingChatRoute

@Serializable data object CustomerServiceRoute

@Serializable data class ChatMessageRote(val targetId: String)

fun NavGraphBuilder.chatGraph() {
    composable(route = PingChatRoute::class) {
        PingChatScreen()
    }

    composable(route = CustomerServiceRoute::class) {
        ChatMessageCustomerServiceScreen()
    }

    composable(route = ChatSystemRoute::class) {
        ChatSystemScreen()
    }

    composable(route = ChatMessageRote::class) {
        val route = it.toRoute<ChatMessageRote>()
        ChatMessageScreen(route.targetId)
    }
}