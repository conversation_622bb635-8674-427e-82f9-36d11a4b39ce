package com.flutterup.app.screen.chat.vm

import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.chat.state.ChatConversation
import com.flutterup.app.screen.chat.state.ChatHomeUiState
import com.flutterup.app.screen.common.vm.PermissionRepository
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.app.utils.chat.ChatConversationComparator
import com.flutterup.app.utils.chat.ChatConversationUtils
import com.flutterup.app.utils.chat.core.ChatMessageMonitor
import com.flutterup.app.utils.chat.core.ChatOnReceivedMessageListener
import com.flutterup.app.utils.chat.core.ChatUnreadMonitor
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChatHomeViewModel
@Inject constructor(
    private val chatHomeRepository: ChatHomeRepository,
    private val chatConversationRepository: ChatConversationRepository,
    private val permissionRepository: PermissionRepository,
    private val unreadMonitor: ChatUnreadMonitor,
    private val userUnreadMonitor: UserUnreadMonitor,
    private val chatMessageMonitor: ChatMessageMonitor,
    private val settingsMonitor: GlobalSettingsMonitor,
) :
    BaseRepositoryViewModel(chatHomeRepository, chatConversationRepository, permissionRepository),
    ChatOnReceivedMessageListener {

    private val _uiState = MutableStateFlow(ChatHomeUiState())

    private val _newConnections = MutableStateFlow<List<UserInfo>>(emptyList())
    private val _conversations = MutableStateFlow<List<ChatConversation>>(emptyList())

    val uiState: StateFlow<ChatHomeUiState> = combine(
        _uiState,
        _newConnections,
        _conversations,
        unreadMonitor.totalSystemMessagesCount,
        userUnreadMonitor.unreadCount,
    ) { ui, newConnections, conversations, totalSystemMessagesCount, userUnreadCount ->
        ChatHomeUiState(
            newConnections = newConnections,
            totalSystemMessagesCount = totalSystemMessagesCount,
            userUnreadCount = userUnreadCount,
            conversations = conversations,


            isRefreshing = ui.isRefreshing,
            isLoadingMore = ui.isLoadingMore,
            hasNoMoreData = ui.hasNoMoreData,
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    init {
        chatMessageMonitor.addMessageListener(this)
        refresh()
    }

    override fun onReceivedMessage(
        message: Message?,
        profile: ReceivedProfile?
    ) {
        if (message == null) return

        if (_newConnections.value.any { it.userId == message.senderUserId }) { //判断是否是新连接的消息
            getNewConnections() //重新刷列表
        }
    }

    fun upgradeNotificationPermission(enable: Boolean) {
        scope.launch {
            permissionRepository.upgradePermissionStatus(noticeStatus = if (enable) PermissionRepository.PERMISSION_ON else PermissionRepository.PERMISSION_OFF)
        }
    }

    fun getNewConnections(): Job {
        return scope.launch {
            val result = chatHomeRepository.getNewConnections()
            _newConnections.update { result ?: emptyList() } //空也会更新
        }
    }

    fun getConversations(timestamp: Long = lastConversationOperationTime): Job {
        return scope.launch {
            val customServiceAccount = settingsMonitor.customerServiceAccount.value
            val noticeAccount = settingsMonitor.noticeAccount.value
            val connections = _newConnections.value.map { it.userId }
            val excludeList = listOfNotNull(
                customServiceAccount,
                noticeAccount,
                *connections.toTypedArray(),
            )

            val conversations = chatConversationRepository.getConversations(timestamp)
            val filteredConversations = conversations?.filterNot {
                //筛选掉
                //1. 提示账号
                //2. 系统账号
                //3. 已经存在的id
                it.targetId in excludeList
            }

            val newConversations = mutableListOf<ChatConversation>()

            for (conversation in _conversations.value) {
                //新筛选后列表里有
                if (filteredConversations?.any { it.targetId == conversation.conversation.targetId } == true) {
                    continue
                }

                newConversations.add(conversation)
            }

            filteredConversations?.forEach { conversation ->
                val existUnreadReceivedGift = ChatConversationUtils.isExistUnreadReceivedGift(conversation)
                newConversations.add(ChatConversation(conversation, existUnreadReceivedGift))
            }
            _conversations.update { newConversations.sortedWith(ChatConversationComparator) }
        }
    }

    fun refresh() {
        scope.launch {
            _uiState.update { it.copy(isRefreshing = true) }

            val jobConnection = getNewConnections()
            val jobConversation = getConversations(timestamp = 0L)

            jobConnection.join()
            jobConversation.join()
        }.invokeOnCompletion {
            _uiState.update { it.copy(isRefreshing = false) }
        }
    }

    fun loadMore() {
        scope.launch {
            _uiState.update { it.copy(isLoadingMore = true) }

            val jobConversation = getConversations()

            jobConversation.join()
        }.invokeOnCompletion {
            _uiState.update { it.copy(isLoadingMore = false) }
        }
    }



    override fun onCleared() {
        chatMessageMonitor.removeMessageListener(this)
        super.onCleared()
    }

    private val lastConversationOperationTime: Long
        get() = _conversations.value.lastOrNull()?.conversation?.operationTime ?: 0L
}