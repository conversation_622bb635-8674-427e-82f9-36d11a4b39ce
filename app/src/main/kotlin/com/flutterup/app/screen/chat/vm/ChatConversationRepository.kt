package com.flutterup.app.screen.chat.vm

import com.flutterup.app.utils.chat.ChatUserInfoCache
import com.flutterup.base.BaseRepository
import com.flutterup.base.utils.resumeIfActive
import com.flutterup.base.utils.resumeWithExceptionIfActive
import com.flutterup.chat.core.ChatException
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Conversation.ConversationType
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject

class ChatConversationRepository @Inject constructor() : BaseRepository() {

    private val count: Int = 10
    private val conversationTypes: Array<ConversationType> = arrayOf(ConversationType.PRIVATE)

    /**
     * 获取会话列表
     */
    suspend fun getConversations(timestamp: Long = 0L): List<Conversation>? = suspendCancellableCoroutine { continuation ->
        RongCoreClient.getInstance().getConversationListByPage(object : IRongCoreCallback.ResultCallback<List<Conversation>>() {
            override fun onSuccess(t: List<Conversation>?) {
                //请求成功
                ChatUserInfoCache.fetchUserinfoList(t?.map { it.targetId }) //去更新数据
                continuation.resumeIfActive(t)
            }

            override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                //请求失败
                continuation.resumeWithExceptionIfActive(ChatException(e))
            }
        }, timestamp, count, *conversationTypes)
    }
}