package com.flutterup.app.screen

import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import androidx.navigation.toRoute
import com.flutterup.app.screen.chat.ChatRoute
import com.flutterup.app.screen.chat.ChatHomeScreen
import com.flutterup.app.screen.discover.DiscoverRoute
import com.flutterup.app.screen.discover.DiscoverScreen
import com.flutterup.app.screen.profile.ProfileRoute
import com.flutterup.app.screen.profile.ProfileScreen
import com.flutterup.app.screen.relate.RelateRoute
import com.flutterup.app.screen.relate.RelateScreen
import kotlinx.serialization.Serializable

@Serializable data object HomeBaseRoute
@OptIn(ExperimentalSharedTransitionApi::class)
fun NavGraphBuilder.homeBottomNavGraph(
    sharedTransitionScope: SharedTransitionScope,
) {
    navigation<HomeBaseRoute>(
        startDestination = DiscoverRoute::class,
    ) {
        composable(route = DiscoverRoute::class) {
            DiscoverScreen(
                sharedTransitionScope = sharedTransitionScope,
                animatedContentScope = this,
            )
        }
        composable(route = RelateRoute::class) {
            val route = it.toRoute<RelateRoute>()

            RelateScreen(index = route.index)
        }

        composable(route = ChatRoute::class) {
            ChatHomeScreen()
        }
        composable(route = ProfileRoute::class) {
            ProfileScreen()
        }
    }
}
