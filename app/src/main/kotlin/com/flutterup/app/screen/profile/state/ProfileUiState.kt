package com.flutterup.app.screen.profile.state

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.flutterup.app.R
import kotlin.enums.EnumEntries

data class ProfileUiState(
    val nickname: String? = null,
    val headimg: String? = null,

    val newVisitorsCount: Int = 0,
    val newVisitorsHeadimg: List<String> = emptyList(),

    //权益相关的
    val isVip: Boolean = false,
    val pp: Int = 0,
    val pv: Int = 0,
    val pingchat: Int = 0,
    val diamond: Double = 0.00,
    val isAly: Boolean = false, //是否是审核模式

    val existNewVersion: Boolean = false,
    val cache: String = "", //app缓存文件大小
    val isCleanCaching: Boolean = false,
) {

    /**
     * 权益列表
     */
    val packs: List<ProfilePacks> get() = if (isAly) {
        listOf(ProfilePacksPingChat(pingchat))
    } else {
        listOf(
            ProfilePacksPhoto(pp),
            ProfilePacksVideo(pv),
            ProfilePacksPingChat(pingchat),
        )
    }

    val options: EnumEntries<ProfileHomeOptions> = ProfileHomeOptions.entries
}

sealed class ProfilePacks(
    @StringRes val titleRes: Int,
    @DrawableRes val iconRes: Int,
    val count: Int,
)

class ProfilePacksPhoto(
    count: Int,
) : ProfilePacks(R.string.private_photos, R.mipmap.ic_packs_picture, count)

class ProfilePacksVideo(
    count: Int,
) : ProfilePacks(R.string.private_videos, R.mipmap.ic_packs_video, count)

class ProfilePacksPingChat(
    count: Int,
) : ProfilePacks(R.string.ping_chat, R.mipmap.ic_packs_pingchat, count)

enum class ProfileHomeOptions(
    @StringRes val titleRes: Int,
    @DrawableRes val iconRes: Int,
) {
    Settings(R.string.setting, R.drawable.ic_profile_home_settings),

    HelpCenter(R.string.help_center, R.drawable.ic_profile_home_helpcenter),

    PrivatePolicy(R.string.privacy_policy, R.drawable.ic_profile_home_privacy_policy),

    TermsPolicy(R.string.terms_policy, R.drawable.ic_profile_home_termservice),

    ChildProtection(R.string.child_policy, 0), //TODO 替换为真实图标

    ClearCache(R.string.clean_cache, R.drawable.ic_settings_clean_cache),

    Version(R.string.version, R.drawable.ic_settings_version),
}