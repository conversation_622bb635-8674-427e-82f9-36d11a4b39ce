@file:OptIn(ExperimentalSharedTransitionApi::class)

package com.flutterup.app.screen.common

import android.net.Uri
import android.os.Bundle
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.toRoute
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.applicationEntryPoint
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.serialization.Serializable
import kotlin.reflect.typeOf

@Serializable data class WebViewRoute(
    val title: String?,
    val url: String,
)

@Serializable
data class MediaPreviewRoute(
    val data: List<MediaItemEntity>,
    val initializeIndex: Int = 0,
)

@Serializable
data object HelperCenterRoute



fun NavGraphBuilder.commonScreen(
    sharedTransitionScope: SharedTransitionScope
) {
    composable<WebViewRoute> { backStackEntry ->
        val webViewRoute = backStackEntry.toRoute<WebViewRoute>()
        CustomWebViewScreen(webViewRoute.title, webViewRoute.url)
    }

    composable<MediaPreviewRoute>(
        typeMap = mapOf(typeOf<List<MediaItemEntity>>() to MediaNavType)
    ) {
        val route = it.toRoute<MediaPreviewRoute>()
        MediaPreviewScreen(
            data = route.data,
            initializeIndex = route.initializeIndex,
            sharedTransitionScope = sharedTransitionScope,
            animatedContentScope = this,
        )
    }

    dialog<HelperCenterRoute> {
        HelperCenterScreen()
    }
}

private object MediaNavType : NavType<List<MediaItemEntity>>(isNullableAllowed = false) {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface MediaNavTypeEntryPoint {
        fun jsonUtils(): JsonUtils
    }

    private val jsonUtils: JsonUtils by lazy {
        applicationEntryPoint<MediaNavTypeEntryPoint>().jsonUtils()
    }


    override fun get(bundle: Bundle, key: String): List<MediaItemEntity> {
        return bundle.getSerializable(key) as List<MediaItemEntity>
    }

    override fun parseValue(value: String): List<MediaItemEntity> {
        return jsonUtils.fromJsonList(value, MediaItemEntity::class.java) ?: emptyList()
    }

    override fun serializeAsValue(value: List<MediaItemEntity>): String {
        return Uri.encode(jsonUtils.toJsonList(value, MediaItemEntity::class.java))
    }

    override fun put(bundle: Bundle, key: String, value: List<MediaItemEntity>) {
        bundle.putSerializable(key, value as java.io.Serializable)
    }
}

