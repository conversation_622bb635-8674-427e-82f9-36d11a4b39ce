@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalClipboard
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavOptions
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.insets.safeArea
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.TextBlack333
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.CustomerServiceRoute
import com.flutterup.base.compose.extension.copyToClipboard
import kotlinx.coroutines.launch

@Composable
fun HelperCenterScreen() {
    val navController = LocalNavController.current
    val clipboard = LocalClipboard.current
    val scope = rememberCoroutineScope()
    val email = stringResource(R.string.support_email)

    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    AppBottomSheetDialog(
        isShown = true,
        onDismissRequest = navController::popBackStack,
        onConfirmRequest = {},
        sheetState = sheetState,
        cancelText = null,
        confirmText = null,
        modifier = Modifier.windowInsetsPadding(WindowInsets.safeArea)
    ) {
        HelperCenterContent(
            onCancelClick = navController::popBackStack,
            onCustomerServiceClick = {
                navController.navigate(CustomerServiceRoute) {
                    popUpTo(HelperCenterRoute) {
                        inclusive = true
                    }
                }
            },
            onCopyEmailClick = {
                scope.launch {
                    clipboard.copyToClipboard(email)
                    navController.popBackStack()
                }
            },
        )
    }
}

@Composable
private fun HelperCenterContent(
    onCancelClick: () -> Unit = {},
    onCustomerServiceClick: () -> Unit = {},
    onCopyEmailClick: () -> Unit = {},
) {
    val shape = RoundedCornerShape(size = 16.dp)

    Box(
        modifier = Modifier
            .background(Color.White, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .padding(bottom = 33.dp)
    ) {
        Column {
            Text(
                text = stringResource(
                    R.string.helper_center_title,
                    stringResource(R.string.support_email)
                ),
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W500,
                    color = TextBlack666,
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier.fillMaxWidth().padding(top = 30.dp)
            )

            Spacer(Modifier.height(23.dp))

            Row(
                modifier = Modifier.fillMaxWidth().padding(start = 18.dp, end = 14.dp),
                horizontalArrangement = Arrangement.spacedBy(7.dp)
            ) {

                Column(
                    modifier = Modifier
                        .weight(1f)
                        .height(104.dp)
                        .background(helperCenterBackgroundColor, shape)
                        .noRippleClickable(onClick = onCustomerServiceClick),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_help_center_customerservice),
                        contentDescription = null,
                        modifier = Modifier.size(53.dp)
                    )

                    Spacer(Modifier.height(6.dp))

                    Text(
                        text = stringResource(R.string.online_support),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBlack333,
                        )
                    )
                }

                Column(
                    modifier = Modifier
                        .weight(1f)
                        .height(104.dp)
                        .background(helperCenterBackgroundColor, shape)
                        .noRippleClickable(onClick = onCopyEmailClick),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_help_center_copy_email),
                        contentDescription = null,
                        modifier = Modifier.size(53.dp)
                    )

                    Spacer(Modifier.height(6.dp))

                    Text(
                        text = stringResource(R.string.copy_email_address),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBlack333,
                        )
                    )
                }
            }

            AppContinueButton(
                onClick = onCancelClick,
                text = stringResource(R.string.cancel),
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .padding(top = 37.dp)
                    .fillMaxWidth()
                    .height(50.dp)
            )
        }
    }
}


private val helperCenterBackgroundColor = Color(0xFFF3F0FF)


@Preview
@Composable
private fun HelperCenterScreenPreview() {
    AppTheme {
        Box(
            Modifier
                .fillMaxSize()
                .background(Color.Gray),
            contentAlignment = Alignment.BottomCenter
        ) {
            HelperCenterContent()
        }
    }
}