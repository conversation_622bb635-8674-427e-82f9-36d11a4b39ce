package com.flutterup.app.screen.chat.component.home

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.times
import androidx.compose.ui.zIndex
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.TextBlack444
import com.flutterup.app.screen.chat.state.ChatHomeUiState
import dev.chrisbanes.haze.HazeStyle
import dev.chrisbanes.haze.hazeEffect
import kotlinx.coroutines.delay
import org.jetbrains.annotations.Async


@Composable
fun ChatHomeTopRow(
    uiState: ChatHomeUiState,
    modifier: Modifier = Modifier,
    onVisitorClick: () -> Unit = {},
    onWinksReceivedClick: () -> Unit = {},
    onNowConnectionsClick: () -> Unit = {},
) {
    val visitors = uiState.userUnreadCount.headList

    // 新头像区域-循环淡出淡入切换头像
    val avatarSwitchIntervalMillis = 3000
    val animationDurationMillis = 450

    var currentAvatarIndex by remember(visitors) { mutableIntStateOf(0) }
    var visible by remember(visitors) { mutableIntStateOf(1) }

    // 切换定时器
    LaunchedEffect(visitors) {
        if (visitors.size > 1) {
            while (true) {
                delay(avatarSwitchIntervalMillis.toLong())
                visible = 0
                delay(animationDurationMillis.toLong())
                currentAvatarIndex = (currentAvatarIndex + 1) % visitors.size
                visible = 1
            }
        }
    }

    val alpha: Float by animateFloatAsState(
        targetValue = if (visible == 1) 1f else 0f,
        animationSpec = tween(durationMillis = animationDurationMillis),
        label = "avatar alpha"
    )


    val visitorModifier = Modifier
        .clip(CircleShape)
        .border(width = 0.5.dp, color = Color.White, shape = CircleShape)
        .padding(0.5.dp)
        .size(20.dp)
        .hazeEffect(
            HazeStyle(
                tint = null,
                blurRadius = 40.dp,
                noiseFactor = 0.15f,
            )
        )

    val connectionModifier = Modifier
        .clip(CircleShape)
        .border(width = 0.5.dp, color = Color.White, shape = CircleShape)
        .padding(0.5.dp)
        .size(20.dp)

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(5.dp)
    ) {
        Box(
            modifier = Modifier
                .weight(120f / 340f)
                .height(80.dp)
                .noRippleClickable(onClick = onVisitorClick),
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_chat_home_visitor_bg),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.matchParentSize()
            )

            Image(
                painter = painterResource(R.mipmap.ic_chat_home_visitor),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier
                    .padding(end = 7.dp, bottom = 6.dp)
                    .size(width = 45.dp, height = 45.dp)
                    .align(Alignment.BottomEnd)
            )

            Column {
                Row(
                    modifier = Modifier.padding(start = 9.dp, top = 5.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.visitor),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.W900,
                            color = Color(0xFF7C1FD6),
                        ),
                    )

                    if (uiState.userUnreadCount.visitorNewNum > 0) {
                        Spacer(modifier = Modifier.width(3.dp))

                        Box {
                            Image(
                                painter = painterResource(R.drawable.ic_chat_visitor_up_label),
                                contentDescription = null,
                                contentScale = ContentScale.FillBounds,
                                modifier = Modifier.matchParentSize()
                            )

                            Text(
                                text = stringResource(R.string.up_new_sub, uiState.userUnreadCount.visitorNewNum.coerceAtMost(99)),
                                style = TextStyle(
                                    fontSize = 10.sp,
                                    fontWeight = FontWeight.W900,
                                    fontStyle = FontStyle.Italic,
                                    color = Color.White,
                                ),
                                modifier = Modifier
                                    .padding(horizontal = 8.dp)
                                    .align(Alignment.Center)
                            )
                        }
                    }
                }

                Row(
                    modifier = Modifier.padding(start = 10.dp, top = 4.dp)
                ) {
                    if (visitors.isNotEmpty()) {
                        AsyncImage(
                            modifier = visitorModifier.alpha(alpha),
                            model = visitors.getOrNull(currentAvatarIndex),
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                        )
                    }
                }
            }

            Text(
                text = stringResource(R.string.chat_visitor_desc),
                style = TextStyle(
                    fontSize = 10.sp,
                    fontWeight = FontWeight.W400,
                    color = TextBlack444,
                ),
                modifier = Modifier
                    .padding(start = 8.dp, bottom = 10.dp)
                    .align(Alignment.BottomStart)
            )
        }

        Box(
            modifier = Modifier
                .weight(120f / 340f)
                .height(80.dp)
                .noRippleClickable(onClick = onWinksReceivedClick)
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_chat_home_winks_bg),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.matchParentSize()
            )

            Image(
                painter = painterResource(R.mipmap.ic_chat_home_winks),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier
                    .padding(end = 6.dp, bottom = 8.7.dp)
                    .size(width = 43.8.dp, height = 42.2.dp)
                    .align(Alignment.BottomEnd)
            )

            Column {
                Row(
                    modifier = Modifier.padding(start = 9.dp, top = 5.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.visitor),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.W900,
                            color = Color(0xFF0035B2),
                        ),
                    )

                    if (uiState.userUnreadCount.visitorNewNum > 0) {
                        Spacer(modifier = Modifier.width(3.dp))

                        Box {
                            Image(
                                painter = painterResource(R.drawable.ic_chat_winks_up_label),
                                contentDescription = null,
                                contentScale = ContentScale.FillBounds,
                                modifier = Modifier.matchParentSize()
                            )

                            Text(
                                text = stringResource(R.string.up_new_sub, uiState.userUnreadCount.winksNewNum.coerceAtMost(99)),
                                style = TextStyle(
                                    fontSize = 10.sp,
                                    fontWeight = FontWeight.W900,
                                    fontStyle = FontStyle.Italic,
                                    color = Color.White,
                                ),
                                modifier = Modifier
                                    .padding(horizontal = 8.dp)
                                    .align(Alignment.Center)
                            )
                        }
                    }
                }

                Image(
                    painter = painterResource(R.drawable.ic_chat_winks),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .padding(start = 11.dp, top = 4.dp)
                        .size(20.dp)
                )
            }

            Text(
                text = stringResource(R.string.chat_winks_desc),
                style = TextStyle(
                    fontSize = 10.sp,
                    fontWeight = FontWeight.W400,
                    color = TextBlack444,
                ),
                modifier = Modifier
                    .padding(start = 8.dp, bottom = 10.dp)
                    .align(Alignment.BottomStart)
            )
        }

        Box(
            modifier = Modifier
                .weight(100f / 340f)
                .height(80.dp)
                .noRippleClickable(onClick = onNowConnectionsClick)
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_chat_home_now_connections_bg),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.matchParentSize()
            )

            Image(
                painter = painterResource(R.mipmap.ic_chat_home_now_connections),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier
                    .size(width = 56.dp, height = 51.dp)
                    .align(Alignment.Center)
            )

            Column(
                modifier = Modifier.matchParentSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.new_match),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(900),
                        color = Color(0xFFC71981),
                    ),
                    modifier = Modifier.padding(top = 5.dp)
                )

                if (uiState.newConnections.isNotEmpty()) {
                    Spacer(Modifier.height(5.dp))

                    Row {
                        //最多只显示2个
                        repeat(uiState.newConnections.size.coerceAtMost(2)) { index ->
                            val connectionUserInfo = uiState.newConnections[index]

                            AsyncImage(
                                modifier = Modifier
                                    .offset((-index * 10f).dp, y = (index * 10).dp)
                                    .scale(1 + 0.5f * index)
                                    .then(connectionModifier),
                                model = connectionUserInfo?.headImage,
                                contentDescription = null,
                                contentScale = ContentScale.Crop,
                            )
                        }
                    }
                }
            }
        }
    }
}