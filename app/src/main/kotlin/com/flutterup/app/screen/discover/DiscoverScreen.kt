@file:OptIn(ExperimentalMaterial3Api::class, ExperimentalSharedTransitionApi::class)

package com.flutterup.app.screen.discover

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.cardstack.CardStackState
import com.flutterup.app.design.component.cardstack.Direction
import com.flutterup.app.design.component.cardstack.rememberCardStackState
import com.flutterup.app.design.modifiers.rememberShimmerOnPrimary
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.DiscoverItemEntity
import com.flutterup.app.model.UserActionType
import com.flutterup.app.model.UserRightsEntity
import com.flutterup.app.model.UserRightsState
import com.flutterup.app.screen.LocalInnerPadding
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.PingChatRoute
import com.flutterup.app.screen.discover.component.DiscoverActionContent
import com.flutterup.app.screen.discover.component.DiscoverCardPager
import com.flutterup.app.screen.discover.component.DiscoverSwipeEmptyContent
import com.flutterup.app.screen.discover.component.DiscoverUserContent
import com.flutterup.app.screen.discover.state.DiscoverState
import com.flutterup.app.screen.discover.vm.DiscoverViewModel
import com.flutterup.app.screen.profile.ProfileOtherPreRoute
import com.flutterup.app.screen.common.vm.ProfileSharedViewModel
import com.flutterup.base.utils.Timber

@Composable
fun DiscoverScreen(
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope,
) {
    val innerPadding = LocalInnerPadding.current
    val navController = LocalNavController.current
    val viewModel = hiltViewModel<DiscoverViewModel>()
    val sharedViewModel = hiltViewModel<ProfileSharedViewModel>()

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val rightState by viewModel.rights.collectAsStateWithLifecycle()

    val topCardCurrentIndex by sharedViewModel.sharedCurrentPage.collectAsStateWithLifecycle()

    val cardStackState = rememberCardStackState()

    LaunchedEffect(Unit) {
        sharedViewModel.actionEvent.collect { event ->
            when(event.type) {
                UserActionType.Dislike -> cardStackState.swipe(Direction.Left)
                UserActionType.Like -> cardStackState.swipe(Direction.Right)
            }
        }
    }

    DiscoverContent(
        innerPadding = innerPadding,
        uiState = uiState,
        rightState = rightState,
        cardStackState = cardStackState,
        sharedTransitionScope = sharedTransitionScope,
        animatedContentScope = animatedContentScope,
        topCardCurrentIndex = topCardCurrentIndex,
        onDrag = { direction, progress ->
            Timber.i("OnDrag", "direction: $direction, progress: $progress")
        },
        onSwiped = { direction, index ->
            viewModel.onSwiped(direction)
        },
        onLikeClick = { cardStackState.swipe(Direction.Right) },
        onDislikeClick = { cardStackState.swipe(Direction.Left) },
        onPingChatClick = { navController.navigate(PingChatRoute) },
        onProfileClick = { item, index ->
            navController.navigate(
                ProfileOtherPreRoute(
                    userId = item.userId.orEmpty(),
                    nickname = item.nickname.orEmpty(),
                    age = item.age ?: 0,
                    headimg = item.headimg.orEmpty(),
                    mediaList = item.mediaList.map { it.imageUrl },
                    intro = item.intro,
                    interests = item.interests,
                    currentIndex = index,
                    from = AppFrom.Discover
                )
            )
        }
    )
}

@Composable
private fun DiscoverContent(
    innerPadding: PaddingValues,
    uiState: DiscoverState,
    rightState: UserRightsState,
    cardStackState: CardStackState,
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope,
    topCardCurrentIndex: Int,
    onDrag: (Direction, Float) -> Unit = { _, _ -> },
    onSwiped: (Direction, Int) -> Unit = { _, _ -> },
    onLikeClick: () -> Unit = {},
    onDislikeClick: () -> Unit = {},
    onPingChatClick: () -> Unit = {},
    onProfileClick: (DiscoverItemEntity, Int) -> Unit = { _, _ -> },
) {
    val shimmerOnPrimary = rememberShimmerOnPrimary()

    AppScaffold(
        canGoBack = false,
        title = { },
        onBackClick = { },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent),
        modifier = Modifier
            .fillMaxSize()
            .padding(bottom = innerPadding.calculateBottomPadding())
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
            )

            if (!rightState.isSwipeEmpty) {
                Column(
                    modifier = Modifier.matchParentSize()

                ) {
                    Spacer(modifier = Modifier.height(24.dp + innerPadding.calculateTopPadding()))

                    DiscoverUserContent(
                        uiState = uiState,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 14.dp)
                    )

                    Spacer(modifier = Modifier.height(22.dp))

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .clip(RoundedCornerShape(topStart = 40.dp, topEnd = 40.dp))
                            .background(Color.White)
                    ) {
                        Spacer(modifier = Modifier.height(44.dp))

                        DiscoverCardPager(
                            uiState = uiState,
                            cardStackState = cardStackState,
                            shimmerOnPrimary = shimmerOnPrimary,
                            sharedTransitionScope = sharedTransitionScope,
                            animatedContentScope = animatedContentScope,
                            topCardCurrentIndex = topCardCurrentIndex,
                            onDrag = onDrag,
                            onSwiped = onSwiped,
                            onProfileClick = onProfileClick
                        )

                        Spacer(modifier = Modifier.height(38.dp))

                        DiscoverActionContent(
                            uiState = uiState,
                            shimmerOnPrimary = shimmerOnPrimary,
                            onLikeClick = onLikeClick,
                            onDislikeClick = onDislikeClick,
                            onPingChatClick = onPingChatClick
                        )

                        Spacer(Modifier.height(24.dp))
                    }
                }
            } else {
                DiscoverSwipeEmptyContent(
                    items = uiState.swipeEmptyList,
                    modifier = Modifier
                        .padding(top = innerPadding.calculateTopPadding())
                        .align(Alignment.Center)
                )
            }
        }
    }
}


@Preview(showSystemUi = true)
@Composable
private fun DiscoverScreenPreview() {
    AppTheme {
        SharedTransitionLayout {
            AnimatedContent("test") {
                DiscoverContent(
                    innerPadding = PaddingValues(0.dp),
                    cardStackState = rememberCardStackState(),
                    sharedTransitionScope = this@SharedTransitionLayout,
                    animatedContentScope = this@AnimatedContent,
                    topCardCurrentIndex = 0,
                    uiState = DiscoverState.EMPTY.copy(
                        headimg = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                        nickname = it
                    ),
                    rightState = UserRightsState(
                        rights = UserRightsEntity(swipeCount = 0, vip = 1)
                    )
                )
            }
        }
    }
}