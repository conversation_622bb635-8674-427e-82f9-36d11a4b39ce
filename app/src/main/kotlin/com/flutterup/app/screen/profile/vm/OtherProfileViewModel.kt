package com.flutterup.app.screen.profile.vm

import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.PingRefer
import com.flutterup.app.model.UserActionCardType
import com.flutterup.app.screen.profile.state.OtherProfileUiState
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class OtherProfileViewModel @Inject constructor(
    private val repository: OtherProfileRepository,
    private val actionRepository: UserActionRepository,
) : BaseRepositoryViewModel(repository) {

    private val _uiState = MutableStateFlow(OtherProfileUiState.EMPTY)

    val uiState: StateFlow<OtherProfileUiState> = _uiState.asStateFlow()

    fun initByParams(
        userId: String,
        nickname: String,
        age: Int,
        headimg: String,
        mediaList: List<String>,
        intro: String?,
        interests: List<String>?
    ) {
        _uiState.update {
            OtherProfileUiState(
                userId = userId,
                headImg = headimg,
                nickname = nickname,
                age = age,
                mediaList = mediaList,
                intro = intro,
                interests = interests,
            )
        }

        getOtherProfileById(userId) //再从后端获取
    }

    fun getOtherProfileById(userId: String) {
        if (userId.isEmpty()) return

        scope.launch {
            val result = repository.getOtherProfileInfo(userId)

            result?.let { result ->
                _uiState.update { state ->
                    state.copy(
                        headImg = result.headImage,
                        nickname = result.nickname,
                        age = result.age,
                        fullMediaList = result.mediaList.orEmpty(),
                        mediaList = result.mediaList?.map { it.imageUrl }.orEmpty(),
                        intro = result.sign,
                        interests = result.tags,
                        refer = PingRefer.fromRefer(result.refer)
                    )
                }
            }
        }
    }

    fun dislike(
        from: AppFrom,
        cardFlag: UserActionCardType = UserActionCardType.Default,
        onSuccess: () -> Unit = {},
    ) {
        scope.launch {
            val isSuccess = actionRepository.dislike(uiState.value.userId, from, cardFlag)
            if (isSuccess) {
                onSuccess()
            }
        }
    }

    fun like(
        from: AppFrom,
        cardFlag: UserActionCardType = UserActionCardType.Default,
        onSuccess: () -> Unit = {},
    ) {
        scope.launch {
            val isSuccess = actionRepository.like(uiState.value.userId, from, cardFlag)
            if (isSuccess) {
                onSuccess()
            }
        }
    }
}