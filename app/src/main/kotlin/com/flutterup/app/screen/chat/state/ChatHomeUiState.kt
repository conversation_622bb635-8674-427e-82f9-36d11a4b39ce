package com.flutterup.app.screen.chat.state

import com.flutterup.app.model.UserCountEntity
import com.flutterup.app.model.UserInfo

data class ChatHomeUiState(

    val userUnreadCount: UserCountEntity = UserCountEntity.EMPTY,

    val totalSystemMessagesCount: Int = 0,

    val newConnections: List<UserInfo?> = emptyList(),


    val isRefreshing: Boolean = false,
    val isLoadingMore: Boolean = false,
    val hasNoMoreData: Boolean = false,
    val conversations: List<ChatConversation> = emptyList(),
) {
}