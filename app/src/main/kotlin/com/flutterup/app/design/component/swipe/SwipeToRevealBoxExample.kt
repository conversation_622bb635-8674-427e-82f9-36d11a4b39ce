package com.flutterup.app.design.component.swipe

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch

/**
 * SwipeToRevealBox 使用示例
 * 展示如何使用滑动显示组件
 */
@Composable
fun SwipeToRevealBoxExample() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "SwipeToRevealBox 示例",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 基础示例
        BasicSwipeExample()
        
        // 单向滑动示例
        SingleDirectionSwipeExample()
    }
}

/**
 * 基础滑动示例
 */
@Composable
private fun BasicSwipeExample() {
    val state = rememberSwipeToRevealBoxState()
    val scope = rememberCoroutineScope()
    
    Column {
        Text(
            text = "基础示例 - 双向滑动",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        SwipeToRevealBox(
            state = state,
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp),
            backgroundContent = {
                // 根据滑动方向显示不同的背景内容
                when (state.dismissDirection) {
                    SwipeToRevealBoxValue.StartToEnd -> {
                        // 向右滑动显示的内容（喜欢）
                        Row(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    lerp(
                                        Color.Transparent,
                                        Color.Green.copy(alpha = 0.3f),
                                        state.progress
                                    )
                                )
                                .padding(horizontal = 16.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Start
                        ) {
                            Icon(
                                imageVector = Icons.Default.Favorite,
                                contentDescription = "喜欢",
                                tint = Color.Green,
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                    SwipeToRevealBoxValue.EndToStart -> {
                        // 向左滑动显示的内容（删除）
                        Row(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    lerp(
                                        Color.Transparent,
                                        Color.Red.copy(alpha = 0.3f),
                                        state.progress
                                    )
                                )
                                .padding(horizontal = 16.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.End
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "删除",
                                tint = Color.Red,
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                    SwipeToRevealBoxValue.Settled -> {
                        // 静止状态不显示背景
                    }
                }
            }
        ) {
            // 主要内容
            Card(
                modifier = Modifier.fillMaxSize(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "滑动我试试看！",
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
        
        // 显示当前状态
        Text(
            text = "当前状态: ${state.currentValue}, 进度: ${"%.2f".format(state.progress)}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

/**
 * 单向滑动示例
 */
@Composable
private fun SingleDirectionSwipeExample() {
    val state = rememberSwipeToRevealBoxState()
    
    Column {
        Text(
            text = "单向滑动示例 - 仅支持左滑",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        SwipeToRevealBox(
            state = state,
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp),
            enableDismissFromStartToEnd = false, // 禁用右滑
            enableDismissFromEndToStart = true,  // 启用左滑
            backgroundContent = {
                if (state.dismissDirection == SwipeToRevealBoxValue.EndToStart) {
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.Blue.copy(alpha = 0.3f))
                            .padding(horizontal = 16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.End
                    ) {
                        Text(
                            text = "删除",
                            color = Color.Blue
                        )
                    }
                }
            }
        ) {
            Card(
                modifier = Modifier.fillMaxSize(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "只能左滑删除",
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
        
        // 显示当前状态
        Text(
            text = "当前状态: ${state.currentValue}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}
