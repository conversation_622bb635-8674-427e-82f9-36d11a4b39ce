package com.flutterup.app.design.component.swipe

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.SpringSpec
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 * 滑动方向枚举，类似于 SwipeToDismissBoxValue
 */
enum class SwipeToRevealBoxValue {
    /** 静止状态 */
    Settled,
    /** 从开始向结束滑动（LTR布局下为左滑，RTL布局下为右滑） */
    StartToEnd,
    /** 从结束向开始滑动（LTR布局下为右滑，RTL布局下为左滑） */
    EndToStart
}

/**
 * SwipeToRevealBox 状态管理器，参考 SwipeToDismissBoxState 的设计
 *
 * @param initialValue 初始状态
 * @param animationSpec 动画规格
 * @param confirmValueChange 状态变化确认回调，返回 false 将阻止状态变化
 */
@Stable
class SwipeToRevealBoxState(
    initialValue: SwipeToRevealBoxValue = SwipeToRevealBoxValue.Settled,
    internal val animationSpec: AnimationSpec<Float> = SpringSpec(),
    internal val confirmValueChange: (SwipeToRevealBoxValue) -> Boolean = { true }
) {
    /**
     * 当前滑动状态
     */
    var currentValue: SwipeToRevealBoxValue by mutableStateOf(initialValue)
        private set

    /**
     * 目标滑动状态
     */
    var targetValue: SwipeToRevealBoxValue by mutableStateOf(initialValue)
        private set

    /**
     * 滑动进度 (0f 到 1f)
     */
    val progress: Float by derivedStateOf {
        val bounds = swipeAnchors
        if (bounds.isEmpty()) return@derivedStateOf 0f

        val currentOffset = offset.value
        val settled = bounds[SwipeToRevealBoxValue.Settled] ?: 0f

        when {
            currentOffset > settled -> {
                val endBound = bounds[SwipeToRevealBoxValue.StartToEnd] ?: settled
                if (endBound == settled) 0f else ((currentOffset - settled) / (endBound - settled)).coerceIn(0f, 1f)
            }
            currentOffset < settled -> {
                val startBound = bounds[SwipeToRevealBoxValue.EndToStart] ?: settled
                if (startBound == settled) 0f else ((settled - currentOffset) / (settled - startBound)).coerceIn(0f, 1f)
            }
            else -> 0f
        }
    }

    /**
     * 滑动方向，基于当前偏移量
     */
    val dismissDirection: SwipeToRevealBoxValue by derivedStateOf {
        val currentOffset = offset.value
        val settled = swipeAnchors[SwipeToRevealBoxValue.Settled] ?: 0f

        when {
            currentOffset > settled -> SwipeToRevealBoxValue.StartToEnd
            currentOffset < settled -> SwipeToRevealBoxValue.EndToStart
            else -> SwipeToRevealBoxValue.Settled
        }
    }

    internal val offset = Animatable(0f)
    internal var swipeAnchors = mapOf<SwipeToRevealBoxValue, Float>()
    internal var density: Float = 1f

    companion object {
        /**
         * 用于保存和恢复状态的 Saver
         */
        fun Saver(
            animationSpec: AnimationSpec<Float>,
            confirmValueChange: (SwipeToRevealBoxValue) -> Boolean
        ) = Saver<SwipeToRevealBoxState, SwipeToRevealBoxValue>(
            save = { it.currentValue },
            restore = { SwipeToRevealBoxState(it, animationSpec, confirmValueChange) }
        )
    }

    internal suspend fun animateTo(
        targetValue: SwipeToRevealBoxValue,
        velocity: Float = 0f
    ) {
        val targetOffset = swipeAnchors[targetValue] ?: return
        this.targetValue = targetValue
        offset.animateTo(targetOffset, animationSpec, velocity) {
            currentValue = swipeAnchors.entries.minByOrNull { abs(it.value - this.value) }?.key ?: targetValue
        }
    }

    internal suspend fun snapTo(targetValue: SwipeToRevealBoxValue) {
        val targetOffset = swipeAnchors[targetValue] ?: return
        this.targetValue = targetValue
        this.currentValue = targetValue
        offset.snapTo(targetOffset)
    }

    internal fun updateAnchors(anchors: Map<SwipeToRevealBoxValue, Float>) {
        swipeAnchors = anchors
    }
}

/**
 * 创建并记住 SwipeToRevealBoxState，参考 rememberSwipeToDismissBoxState
 *
 * @param initialValue 初始状态
 * @param animationSpec 动画规格
 * @param confirmValueChange 状态变化确认回调
 */
@Composable
fun rememberSwipeToRevealBoxState(
    initialValue: SwipeToRevealBoxValue = SwipeToRevealBoxValue.Settled,
    animationSpec: AnimationSpec<Float> = SpringSpec(),
    confirmValueChange: (SwipeToRevealBoxValue) -> Boolean = { true }
): SwipeToRevealBoxState {
    return rememberSaveable(
        saver = SwipeToRevealBoxState.Saver(
            animationSpec = animationSpec,
            confirmValueChange = confirmValueChange
        )
    ) {
        SwipeToRevealBoxState(
            initialValue = initialValue,
            animationSpec = animationSpec,
            confirmValueChange = confirmValueChange
        )
    }
}

/**
 * SwipeToRevealBox 滑动显示组件，参考 SwipeToDismissBox 的实现
 * 支持左右滑动手势来显示背景内容
 *
 * @param state 滑动状态管理器
 * @param modifier 修饰符
 * @param enableDismissFromStartToEnd 是否启用从开始向结束滑动
 * @param enableDismissFromEndToStart 是否启用从结束向开始滑动
 * @param gestureThreshold 手势阈值，超过此值将触发状态变化
 * @param backgroundContent 背景内容，在滑动时显示
 * @param content 主要内容
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SwipeToRevealBox(
    state: SwipeToRevealBoxState,
    modifier: Modifier = Modifier,
    enableDismissFromStartToEnd: Boolean = true,
    enableDismissFromEndToStart: Boolean = true,
    gestureThreshold: Dp = 56.dp,
    backgroundContent: @Composable RowScope.() -> Unit,
    content: @Composable RowScope.() -> Unit
) {
    val density = LocalDensity.current
    val scope = rememberCoroutineScope()

    // 计算滑动锚点
    val thresholdPx = with(density) { gestureThreshold.toPx() }
    val anchors = remember(thresholdPx, enableDismissFromStartToEnd, enableDismissFromEndToStart) {
        mutableMapOf<SwipeToRevealBoxValue, Float>().apply {
            put(SwipeToRevealBoxValue.Settled, 0f)
            if (enableDismissFromStartToEnd) {
                put(SwipeToRevealBoxValue.StartToEnd, thresholdPx)
            }
            if (enableDismissFromEndToStart) {
                put(SwipeToRevealBoxValue.EndToStart, -thresholdPx)
            }
        }
    }

    // 更新状态中的锚点和密度
    LaunchedEffect(anchors, density.density) {
        state.updateAnchors(anchors)
        state.density = density.density
    }

    // 拖拽状态处理
    val draggableState = rememberDraggableState { delta ->
        scope.launch {
            val newOffset = state.offset.value + delta

            // 检查是否允许当前方向的滑动
            val isAllowed = when {
                delta > 0 -> enableDismissFromStartToEnd // 向右滑动
                delta < 0 -> enableDismissFromEndToStart // 向左滑动
                else -> true
            }

            if (isAllowed) {
                // 限制滑动范围
                val minOffset = anchors[SwipeToRevealBoxValue.EndToStart] ?: 0f
                val maxOffset = anchors[SwipeToRevealBoxValue.StartToEnd] ?: 0f
                val constrainedOffset = newOffset.coerceIn(minOffset, maxOffset)

                state.offset.snapTo(constrainedOffset)
            }
        }
    }

    Box(
        modifier = modifier,
        propagateMinConstraints = true
    ) {
        // 背景内容
        Row(content = backgroundContent, modifier = Modifier.matchParentSize())

        // 主要内容，带有滑动手势
        Row (
            modifier = Modifier
                .fillMaxSize()
                .offset {
                    IntOffset(
                        x = state.offset.value.roundToInt(),
                        y = 0
                    )
                }
                .draggable(
                    state = draggableState,
                    orientation = Orientation.Horizontal,
                    onDragStopped = { velocity ->
                        scope.launch {
                            val currentOffset = state.offset.value
                            val settledOffset = anchors[SwipeToRevealBoxValue.Settled] ?: 0f

                            // 根据滑动距离和速度决定目标状态
                            val targetValue = when {
                                // 基于速度的判断
                                abs(velocity) > 500f -> {
                                    when {
                                        velocity > 0 && enableDismissFromStartToEnd -> SwipeToRevealBoxValue.StartToEnd
                                        velocity < 0 && enableDismissFromEndToStart -> SwipeToRevealBoxValue.EndToStart
                                        else -> SwipeToRevealBoxValue.Settled
                                    }
                                }
                                // 基于距离的判断
                                else -> {
                                    val absOffset = abs(currentOffset - settledOffset)
                                    val threshold = thresholdPx * 0.5f // 50% 阈值

                                    when {
                                        absOffset < threshold -> SwipeToRevealBoxValue.Settled
                                        currentOffset > settledOffset && enableDismissFromStartToEnd -> SwipeToRevealBoxValue.StartToEnd
                                        currentOffset < settledOffset && enableDismissFromEndToStart -> SwipeToRevealBoxValue.EndToStart
                                        else -> SwipeToRevealBoxValue.Settled
                                    }
                                }
                            }

                            // 确认状态变化
                            if (state.confirmValueChange(targetValue)) {
                                state.animateTo(targetValue, velocity)
                            } else {
                                // 不允许状态变化，回到静止状态
                                state.animateTo(SwipeToRevealBoxValue.Settled, velocity)
                            }
                        }
                    }
                ),
            content = content
        )
    }
}

