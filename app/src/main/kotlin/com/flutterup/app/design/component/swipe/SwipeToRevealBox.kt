package com.flutterup.app.design.component.swipe

import androidx.annotation.FloatRange
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.material3.internal.AnchoredDraggableDefaults
import androidx.compose.material3.internal.AnchoredDraggableState
import androidx.compose.material3.internal.DraggableAnchors
import androidx.compose.material3.internal.anchoredDraggable
import androidx.compose.material3.internal.animateTo
import androidx.compose.material3.internal.draggableAnchors
import androidx.compose.material3.internal.snapTo
import androidx.compose.runtime.Composable
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.CancellationException

private val RevealVelocityThreshold = 125.dp

/** SwipeToRevealBox 的滑动方向枚举 */
enum class SwipeToRevealBoxValue {
    /** 从开始向结束滑动（显示开始侧的 reveal 内容） */
    StartToEnd,

    /** 从结束向开始滑动（显示结束侧的 reveal 内容） */
    EndToStart,

    /** 静止状态（不显示 reveal 内容） */
    Settled
}

/**
 * SwipeToRevealBox 的状态管理器，参考 SwipeToDismissBoxState 实现
 *
 * @param initialValue 初始状态值
 * @param density 密度，用于 dp 和 px 之间的转换
 * @param confirmValueChange 可选的回调，用于确认或否决状态变化
 * @param positionalThreshold 位置阈值，用于计算滑动过程中的目标状态和滑动结束后的结算
 */
class SwipeToRevealBoxState(
    initialValue: SwipeToRevealBoxValue,
    internal val density: Density,
    confirmValueChange: (SwipeToRevealBoxValue) -> Boolean = { true },
    positionalThreshold: (totalDistance: Float) -> Float
) {
    internal val anchoredDraggableState =
        AnchoredDraggableState(
            initialValue = initialValue,
            animationSpec = AnchoredDraggableDefaults.AnimationSpec,
            confirmValueChange = confirmValueChange,
            positionalThreshold = positionalThreshold,
            velocityThreshold = { with(density) { RevealVelocityThreshold.toPx() } }
        )

    internal val offset: Float
        get() = anchoredDraggableState.offset

    /**
     * 获取当前偏移量，如果偏移量尚未初始化则抛出异常
     *
     * @throws IllegalStateException 如果偏移量尚未初始化
     */
    fun requireOffset(): Float = anchoredDraggableState.requireOffset()

    /** SwipeToRevealBoxState 的当前状态值 */
    val currentValue: SwipeToRevealBoxValue
        get() = anchoredDraggableState.currentValue

    /**
     * 目标状态。这是最接近当前偏移量的状态（考虑位置阈值）。
     * 如果没有正在进行的交互（如动画或拖拽），这将是当前状态。
     */
    val targetValue: SwipeToRevealBoxValue
        get() = anchoredDraggableState.targetValue

    /**
     * 从 currentValue 到 targetValue 的进度比例，范围在 [0f..1f] 内
     */
    @get:FloatRange(from = 0.0, to = 1.0)
    val progress: Float
        get() = anchoredDraggableState.progress

    /**
     * 组件被滑动或正在被滑动的方向（如果有的话）
     *
     * 如果你想在每一侧有不同的操作，可以使用此属性来改变 SwipeToRevealBox 的背景
     */
    val dismissDirection: SwipeToRevealBoxValue
        get() =
            when {
                offset == 0f || offset.isNaN() -> SwipeToRevealBoxValue.Settled
                offset > 0f -> SwipeToRevealBoxValue.StartToEnd
                else -> SwipeToRevealBoxValue.EndToStart
            }

    /**
     * 无动画地设置状态并挂起直到设置完成
     *
     * @param targetValue 新的目标值
     */
    suspend fun snapTo(targetValue: SwipeToRevealBoxValue) {
        anchoredDraggableState.snapTo(targetValue)
    }

    /**
     * 通过动画将组件重置到默认位置并挂起直到完全重置或动画被取消。
     * 如果动画被中断，此方法将抛出 [CancellationException]
     *
     * @return 重置动画结束的原因
     */
    suspend fun reset() =
        anchoredDraggableState.animateTo(targetValue = SwipeToRevealBoxValue.Settled)

    /**
     * 在给定方向上显示组件，带有动画并挂起。
     * 如果动画被中断，此方法将抛出 [CancellationException]
     *
     * @param direction 显示方向
     */
    suspend fun reveal(direction: SwipeToRevealBoxValue) {
        anchoredDraggableState.animateTo(targetValue = direction)
    }

    companion object {
        /** SwipeToRevealBoxState 的默认 [Saver] 实现 */
        fun Saver(
            confirmValueChange: (SwipeToRevealBoxValue) -> Boolean,
            positionalThreshold: (totalDistance: Float) -> Float,
            density: Density
        ) =
            Saver<SwipeToRevealBoxState, SwipeToRevealBoxValue>(
                save = { it.currentValue },
                restore = {
                    SwipeToRevealBoxState(it, density, confirmValueChange, positionalThreshold)
                }
            )
    }
}

/**
 * 创建并 [remember] 一个 [SwipeToRevealBoxState]
 *
 * @param initialValue 状态的初始值
 * @param confirmValueChange 可选的回调，用于确认或否决状态变化
 * @param positionalThreshold 位置阈值，用于计算滑动过程中的目标状态和滑动结束后的结算
 */
@Composable
fun rememberSwipeToRevealBoxState(
    initialValue: SwipeToRevealBoxValue = SwipeToRevealBoxValue.Settled,
    confirmValueChange: (SwipeToRevealBoxValue) -> Boolean = { true },
    positionalThreshold: (totalDistance: Float) -> Float =
        SwipeToRevealBoxDefaults.positionalThreshold,
): SwipeToRevealBoxState {
    val density = LocalDensity.current
    return rememberSaveable(
        saver =
            SwipeToRevealBoxState.Saver(
                confirmValueChange = confirmValueChange,
                density = density,
                positionalThreshold = positionalThreshold
            )
    ) {
        SwipeToRevealBoxState(initialValue, density, confirmValueChange, positionalThreshold)
    }
}

/**
 * 可以通过左右滑动来显示背景内容的组件
 *
 * @param state 此组件的状态
 * @param backgroundContent 堆叠在 [content] 后面的可组合项，在内容被滑动时显示。
 *   你可以/应该使用 [state] 在每一侧有不同的背景
 * @param modifier 此组件的可选 [Modifier]
 * @param enableRevealFromStartToEnd 是否可以从开始向结束滑动来显示内容
 * @param enableRevealFromEndToStart 是否可以从结束向开始滑动来显示内容
 * @param gesturesEnabled 是否可以通过手势与滑动显示进行交互
 * @param revealSize 背景内容显示的最大宽度
 * @param content 可以被滑动的内容
 */
@Composable
fun SwipeToRevealBox(
    state: SwipeToRevealBoxState,
    backgroundContent: @Composable RowScope.() -> Unit,
    modifier: Modifier = Modifier,
    enableRevealFromStartToEnd: Boolean = true,
    enableRevealFromEndToStart: Boolean = true,
    gesturesEnabled: Boolean = true,
    revealSize: Dp = 80.dp,
    content: @Composable RowScope.() -> Unit,
) {
    val isRtl = LocalLayoutDirection.current == LayoutDirection.Rtl
    val density = LocalDensity.current
    val revealSizePx = with(density) { revealSize.toPx() }

    Box(
        modifier.anchoredDraggable(
            state = state.anchoredDraggableState,
            orientation = Orientation.Horizontal,
            enabled = gesturesEnabled && state.currentValue == SwipeToRevealBoxValue.Settled,
        ),
        propagateMinConstraints = true
    ) {
        Row(content = backgroundContent, modifier = Modifier.matchParentSize())
        Row(
            content = content,
            modifier =
                Modifier.draggableAnchors(state.anchoredDraggableState, Orientation.Horizontal) {
                    size,
                    _ ->
                    return@draggableAnchors DraggableAnchors {
                        SwipeToRevealBoxValue.Settled at 0f
                        if (enableRevealFromStartToEnd) {
                            SwipeToRevealBoxValue.StartToEnd at (if (isRtl) -revealSizePx else revealSizePx)
                        }
                        if (enableRevealFromEndToStart) {
                            SwipeToRevealBoxValue.EndToStart at (if (isRtl) revealSizePx else -revealSizePx)
                        }
                    } to state.targetValue
                }
        )
    }
}

/** 包含 [SwipeToRevealBox] 和 [SwipeToRevealBoxState] 的默认值 */
object SwipeToRevealBoxDefaults {
    /** SwipeToRevealBoxState 的默认位置阈值 56.dp */
    val positionalThreshold: (totalDistance: Float) -> Float
        @Composable get() = with(LocalDensity.current) { { 56.dp.toPx() } }
}

