package com.flutterup.app

import coil3.ImageLoader
import coil3.PlatformContext
import coil3.SingletonImageLoader
import coil3.disk.DiskCache
import coil3.disk.directory
import coil3.request.crossfade
import com.flutterup.app.utils.GlobalCoroutineExceptionHandler
import com.flutterup.base.BaseApplication
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineExceptionHandler

@HiltAndroidApp
class FlutterUpApplication : BaseApplication(), SingletonImageLoader.Factory {

    override fun exceptionHandler(): CoroutineExceptionHandler {
        return GlobalCoroutineExceptionHandler
    }

    override fun newImageLoader(context: PlatformContext): ImageLoader {
        return ImageLoader.Builder(context)
            .diskCache {
                DiskCache.Builder()
                    .directory(context.cacheDir.resolve("app_cache").resolve("coil"))
                    .build()
            }
            .build()
    }
}