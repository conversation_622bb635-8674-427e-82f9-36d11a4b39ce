package com.flutterup.base.utils

import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.Period
import java.util.Calendar

object DateUtils {

    private const val MILLISECONDS_TIMESTAMP = 10000000000L
    private const val MILLISECONDS = 1000
    private const val MINUTES = 60
    private const val HOURS = 60
    private const val DAYS = 24
    private const val YEARS = 365
    private const val ONE_DAY = DAYS * HOURS * MINUTES * MILLISECONDS

    /**
     * 服务端常用的时间format
     */
    val defaultServerDateFormat = SimpleDateFormat("yyyy-MM-dd", LocaleUtils.currentLocale)
    val defaultServerDateTimeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", LocaleUtils.currentLocale)

    val defaultFileDateFormat = SimpleDateFormat("yyyyMMddHHmmss", LocaleUtils.currentLocale)
    val defaultHourMinuteFormat = SimpleDateFormat("hh:mm a", LocaleUtils.currentLocale)

    fun formatTimestamp(timestamp: Long, format: SimpleDateFormat = defaultServerDateTimeFormat): String {
        // Automatically detect if timestamp is in seconds (10 digits) or milliseconds (13 digits)
        val timestamp = if (timestamp < MILLISECONDS_TIMESTAMP) timestamp * 1000 else timestamp

        return try {
            val calendar = Calendar.getInstance(LocaleUtils.currentLocale).apply {
                timeInMillis = timestamp
            }
            format.format(calendar.time)
        } catch (_: Exception) {
            ""
        }
    }


    fun timestamp2Ago(timestamp: Long?): String {
        if (timestamp == null) return ""

        val currentTime = System.currentTimeMillis()
        val diffTime = currentTime - timestamp

        return if (diffTime < ONE_DAY) {
            // 不足一天显示具体时间 (14:12)
            formatTimestamp(timestamp, defaultHourMinuteFormat)
        } else {
            // 计算天数
            val days = (diffTime / ONE_DAY).toInt()
            // 显示"x days ago"
            if (days == 1) "1 day ago" else "$days days ago"
        }
    }


    fun formatBirthday(birthday: Long, format: SimpleDateFormat = defaultServerDateFormat): String {
        return format.format(birthday)
    }

    fun getAgeFromLocalDate(date: LocalDate): Int {
        val today = LocalDate.now()
        return Period.between(date, today).years
    }
}