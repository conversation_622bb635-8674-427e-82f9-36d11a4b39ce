package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag
import kotlin.Any

@MessageTag(value = PublicMessageContent.VALUE, flag = MessageTag.ISCOUNTED)
class PublicMessageContent : BaseCustomMessageContent {

    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(source: Parcel?) : super(source)

    companion object {
        const val VALUE = "connectfriends:public"

        @JvmField
        val CREATOR: Parcelable.Creator<PublicMessageContent> = object : Parcelable.Creator<PublicMessageContent> {
            override fun createFromParcel(source: Parcel?): PublicMessageContent? {
                return PublicMessageContent(source)
            }

            override fun newArray(size: Int): Array<out PublicMessageContent?>? {
                return arrayOfNulls(size)
            }
        }

        @JvmStatic
        fun obtain(content: String?) = PublicMessageContent(content)
    }
}