package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag


@MessageTag(value = GiftMessageContent.VALUE, flag = MessageTag.ISCOUNTED)
class GiftMessageContent : BaseCustomMessageContent {

    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(source: Parcel?) : super(source)

    companion object {
        const val VALUE = "CM:gift"

        @JvmField
        val CREATOR: Parcelable.Creator<GiftMessageContent> = object : Parcelable.Creator<GiftMessageContent> {
            override fun createFromParcel(source: Parcel?): GiftMessageContent? {
                return GiftMessageContent(source)
            }

            override fun newArray(size: Int): Array<out GiftMessageContent?>? {
                return arrayOfNulls(size)
            }
        }

        @JvmStatic
        fun obtain(content: String?) = GiftMessageContent(content)
    }
}