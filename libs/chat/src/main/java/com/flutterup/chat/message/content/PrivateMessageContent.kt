package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag

/**
 * chatter发送的视频消息，以及fascin8发送过来的SightMsg
 * （融云小视频消息SightMsg，服务器拦截转发时，转成了connectfriends:privacy）
 */
@MessageTag(value = PrivateMessageContent.VALUE, flag = MessageTag.ISCOUNTED)
class PrivateMessageContent : BaseCustomMessageContent {

    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(source: Parcel?) : super(source)

    companion object {

        const val VALUE = "connectfriends:privacy"

        @JvmField
        val CREATOR: Parcelable.Creator<PrivateMessageContent> = object : Parcelable.Creator<PrivateMessageContent> {
            override fun createFromParcel(source: Parcel?): PrivateMessageContent? {
                return PrivateMessageContent(source)
            }

            override fun newArray(size: Int): Array<out PrivateMessageContent?>? {
                return arrayOfNulls(size)
            }
        }

        @JvmStatic
        fun obtain(content: String?) = PrivateMessageContent(content)
    }
}