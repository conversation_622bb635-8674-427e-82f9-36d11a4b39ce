package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag


//系统触发的行为消息，比如系统推的like，或者 cupidchat，不存储，不计数
@MessageTag(value = SystemActionMessageContent.VALUE , flag = MessageTag.STATUS)
class SystemActionMessageContent : BaseCustomMessageContent {
    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(source: Parcel?) : super(source)

    companion object {
        const val VALUE = "connectfriends:sysaction"

        @JvmField
        val CREATOR: Parcelable.Creator<SystemActionMessageContent> = object : Parcelable.Creator<SystemActionMessageContent> {
            override fun createFromParcel(source: Parcel?): SystemActionMessageContent? {
                return SystemActionMessageContent(source)
            }

            override fun newArray(size: Int): Array<out SystemActionMessageContent?>? {
                return arrayOfNulls<SystemActionMessageContent>(size)
            }
        }

        @JvmStatic
        fun obtain(content: String?): SystemActionMessageContent = SystemActionMessageContent(content)
    }
}