package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag

@MessageTag(value = DataSyncMessageContent.VALUE, flag = MessageTag.STATUS)
class DataSyncMessageContent : BaseCustomMessageContent {

    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(`in`: Parcel?) : super(`in`)

    companion object {
        const val VALUE = "connectfriends:datasyn"

        @JvmField
        val CREATOR: Parcelable.Creator<DataSyncMessageContent> = object : Parcelable.Creator<DataSyncMessageContent> {
            override fun createFromParcel(source: Parcel): DataSyncMessageContent {
                return DataSyncMessageContent(source)
            }

            override fun newArray(size: Int): Array<DataSyncMessageContent?> {
                return arrayOfNulls(size)
            }
        }
    }
}