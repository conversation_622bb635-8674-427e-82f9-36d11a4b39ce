package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag

@MessageTag(value = ConnectMessageContent.VALUE, flag = MessageTag.ISPERSISTED)
class ConnectMessageContent : BaseCustomMessageContent {


    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(source: Parcel?) : super(source)

    companion object {
        const val VALUE = "connectfriends:connected"

        @JvmField
        val CREATOR: Parcelable.Creator<ConnectMessageContent> = object : Parcelable.Creator<ConnectMessageContent> {
            override fun createFromParcel(source: Parcel?): ConnectMessageContent {
                return ConnectMessageContent(source)
            }

            override fun newArray(size: Int): Array<ConnectMessageContent?> {
                return arrayOfNulls(size)
            }
        }

        @JvmStatic
        fun obtain(content: String?) = ConnectMessageContent(content)
    }
}